# Generated by Django 4.2.18 on 2025-06-19 18:26

from django.db import migrations, models
import uuid
import wagtail.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Character',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='Character Name')),
                ('role', models.TextField(verbose_name='Role')),
                ('tone', models.TextField(verbose_name='Tone')),
                ('background', models.TextField(verbose_name='Background')),
                ('few_shots', wagtail.fields.StreamField([('shot', 0)], blank=True, block_lookup={0: ('wagtail.blocks.CharBlock', (), {'label': 'Few-Shot Example'})}, verbose_name='Few-Shot Examples')),
            ],
        ),
        migrations.CreateModel(
            name='RAGFile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file_name', models.CharField(max_length=255, verbose_name='File Name')),
                ('weaviate_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Weaviate ID')),
                ('s3_object_key', models.CharField(blank=True, max_length=255, null=True, verbose_name='S3 Object Key')),
                ('temp_file', models.FileField(blank=True, null=True, upload_to='temp/', verbose_name='Upload File')),
            ],
        ),
    ]
