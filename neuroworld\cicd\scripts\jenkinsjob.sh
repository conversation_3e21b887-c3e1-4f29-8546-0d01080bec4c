#!/bin/bash
set -e

echo Building the Docker image...
docker build \
--build-arg ECR_REPOSITORY_URI=${ECR_REPOSITORY_BASE_URI}/${IMAGE} \
--build-arg TAG=$TAG \
-t $CONTAINER_NAME:latest \
-f $DOCKERFILEPATH neuroworld/

docker system prune -f
docker system prune --volumes -f
docker image prune -f
docker images

COMMIT_HASH=$(echo $GIT_COMMIT | cut -c 1-7)
BRANCH_TAG=$(echo $GIT_BRANCH | cut -d'/' -f2)
IMAGE_TAG=$BRANCH_TAG-$COMMIT_HASH

docker tag $CONTAINER_NAME:latest $ECR_REPOSITORY_ENV_URI:latest
docker tag $CONTAINER_NAME:latest $ECR_REPOSITORY_ENV_URI:$IMAGE_TAG
docker push --all-tags $ECR_REPOSITORY_ENV_URI

echo Build completed on `date`
echo Stopping Previous $CONTAINER_NAME Container....

if docker ps --filter "name=$CONTAINER_NAME" --format "{{.Names}}" | grep -w $CONTAINER_NAME > /dev/null; then
    docker stop $CONTAINER_NAME  || true
    sleep 10
else
    echo "Container $CONTAINER_NAME is not running."
fi

FIREBASE_CREDENTIALS=$(cat $FIREBASE_KEY_FILE)
ENV_CREDENTIALS=$(cat $ENV_FILE)
GOOGLE_PLAY_SERVICE_ACCOUNT_CREDENTIALS=$(cat $GOOGLE_PLAY_SERVICE_ACCOUNT_FILE)

echo Running Updated $CONTAINER_NAME Container....
#--env-file $ENV_FILE \
docker run -d --rm \
--env ENV_CREDENTIALS="$ENV_CREDENTIALS" \
--env FIREBASE_CREDENTIALS="$FIREBASE_CREDENTIALS" \
--env GOOGLE_PLAY_SERVICE_ACCOUNT_CREDENTIALS="$GOOGLE_PLAY_SERVICE_ACCOUNT_CREDENTIALS" \
--name $CONTAINER_NAME \
-p 8001:80 \
$ECR_REPOSITORY_ENV_URI:latest

rm -rf ./neuroworld/.env
echo Logging out from ECR..
docker logout $ECR_REPOSITORY_BASE_URI
docker rmi $CONTAINER_NAME:latest $ECR_REPOSITORY_ENV_URI:$IMAGE_TAG
docker ps -a
echo Container started kindly check now using $APP_BASE_URL
