[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
annotated-types = "==0.7.0"
anyio = "*"
argon2-cffi = "==23.1.0"
argon2-cffi-bindings = "==21.2.0"
asgiref = "==3.8.1"
attrs = "==24.3.0"
cachetools = "==5.5.1"
certifi = "==2024.12.14"
cffi = "==1.17.1"
charset-normalizer = "==3.4.1"
distro = "==1.9.0"
dj-database-url = "==2.3.0"
django = "==4.2.18"
django-cors-headers = "==3.14.0"
django-environ = "==0.12.0"
django-filter = "==23.3"
django-model-utils = "==4.5.1"
djangorestframework = "==3.15.2"
djangorestframework-simplejwt = "==5.4.0"
drf-spectacular = "==0.28.0"
drf-yasg = "==1.21.8"
google-auth = "==2.37.0"
gunicorn = "==20.1.0"
h11 = "==0.14.0"
httpcore = "==1.0.7"
httpx = "==0.28.1"
idna = "==3.10"
inflection = "==0.5.1"
jiter = "==0.8.2"
jsonschema = "==4.23.0"
jsonschema-specifications = "==2024.10.1"
openai = "==1.59.9"
packaging = "==24.2"
psycopg2-binary = "==2.9.10"
pyasn1 = "==0.6.1"
pyasn1-modules = "==0.4.1"
pycparser = "==2.22"
pydantic = "==2.10.5"
pydantic-core = "==2.27.2"
pytz = "==2022.7.1"
pyyaml = "==6.0.2"
referencing = "==0.36.1"
requests = "==2.32.3"
rpds-py = "==0.22.3"
rsa = "==4.9"
sniffio = "==1.3.1"
sqlparse = "==0.5.3"
tqdm = "==4.67.1"
typing-extensions = "==4.12.2"
uritemplate = "==4.1.1"
urllib3 = "==2.3.0"
whitenoise = "==6.8.2"
boto3 = "==1.36.15"
botocore = "==1.36.15"
jmespath = "==1.0.1"
python-dateutil = "==2.9.0.post0"
s3transfer = "==0.11.2"
six = "==1.17.0"
django-storages = "==1.14.6"
tzdata = "==2025.2"
pillow = "==11.2.1"
django-celery-beat = "==2.8.0"
firebase-admin = "==6.8.0"
pyfcm = "==2.0.8"
aiohappyeyeballs = "==2.6.1"
aiohttp = "==3.11.18"
aiosignal = "==1.3.2"
amqp = "==5.3.1"
billiard = "==4.2.1"
cachecontrol = "==0.14.2"
cryptography = "==44.0.2"
django-timezone-field = "==7.1"
google-api-python-client = "==2.169.0"
google-auth-httplib2 = "==0.2.0"
google-cloud-core = "==2.4.3"
google-cloud-firestore = "==2.20.2"
google-cloud-storage = "==3.1.0"
google-crc32c = "==1.7.1"
google-resumable-media = "==2.7.2"
googleapis-common-protos = "==1.70.0"
grpcio = "==1.71.0"
grpcio-status = "==1.71.0"
httplib2 = "==0.22.0"
msgpack = "==1.1.0"
proto-plus = "==1.26.1"
protobuf = "==5.29.4"
pyparsing = "==3.2.3"
vine = "==5.1.0"
wcwidth = "==0.2.13"
yarl = "==1.20.0"
python-dotenv = "*"
huggingface-hub = "*"
python-multipart = "*"
langchain = "*"
langchain-openai = "*"
langchain-pinecone = "*"
langchain-community = "*"
langchain-huggingface = "*"
weaviate-client = "==3.25.3"
langgraph = "*"
celery = {extras = ["sqs"], version = "==5.5.2"}
google-api-core = {extras = ["grpc"], version = "==2.24.2"}
pyjwt = {extras = ["crypto"], version = "==2.10.1"}
wagtail = "*"

[dev-packages]
isort = "~=5.12"
black = "~=22.10"  # https://github.com/psf/black
pre-commit = "~=2.20"  # https://github.com/pre-commit/pre-commit
pytest-django = "~=4.5"  # https://github.com/pytest-dev/pytest-django

[requires]
python_version = "3.11"
