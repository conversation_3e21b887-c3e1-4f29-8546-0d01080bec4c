import os
import json
from django.core.management.base import BaseCommand
from apps.neuroworld.models import Notification, NotificationType, NotificationSubType
import uuid

class Command(BaseCommand):
    help = 'Seed Notification records from JSON file'

    def handle(self, *args, **kwargs):
        # Define the path to the JSON file
        file_path = os.path.join(os.path.dirname(__file__), "../../fixtures/notifications_data.json")

        # Read the JSON data from the file
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                notification_data = json.load(file)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error reading JSON file: {str(e)}"))
            return

        # Seed Notification records from the loaded JSON data
        for notification in notification_data:
            # Validate type and subtype against the enum choices
            if notification['type'] not in NotificationType.values:
                self.stdout.write(self.style.ERROR(f"Invalid 'type' value: {notification['type']}"))
                continue

            if notification['sub_type'] not in NotificationSubType.values:
                self.stdout.write(self.style.ERROR(f"Invalid 'sub_type' value: {notification['sub_type']}"))
                continue

            # Create or update the notification
            try:
                Notification.objects.get_or_create(
                    sub_type=notification['sub_type'],
                    defaults={
                        'type': notification['type'],
                        'title': notification['title'],
                        'icon': notification['icon'],
                        'message': notification['message']
                    }
                )
            except KeyError as e:
                self.stdout.write(self.style.ERROR(f'Missing required key: {e} in notification data'))
                continue

        self.stdout.write(self.style.SUCCESS('Seeded Notification records successfully from JSON file.'))
