# urls.py
from django.test import tag
from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from .views import AccountDeletionRequestView, AnalyticsView, CustomTokenObtainPairView, EditGoalView, ForgotPasswordView, GoalTrackingView, GoalVersionViewSet, GoalViewSet, GoogleView, AppleView, LogoutView, NeuroChatViewSet, ProfilePictureUploadView, ResetChatView, RestoreGoalView, SelectGoalView, SelectedGoalViewSet, SubscriptionPlansView, UpdateFCMTokenView, UpdatePasswordView, UserNotificationListView, UserProfileViewSet, UserViewSet, chat_view, apple_iap_webhook, me, send_feedback_email, stream_chat, DeleteAccountView, GooglePlayRTDNWebhook

router = DefaultRouter()
router.register(r'users', UserViewSet, basename='user') # user + profile
router.register(r'user/profile', UserProfileViewSet, basename='user-profile') #user + profile
router.register(r'chats', NeuroChatViewSet)
router.register(r'goal-versions', GoalVersionViewSet)
router.register(r'goal', GoalViewSet)
router.register(r'selected-goals', SelectedGoalViewSet, basename='selectedgoal')

urlpatterns = [
    path('token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'), #user + profile
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('google/token/', GoogleView.as_view({'post': 'post'}), name='google_verify'),
    path('apple/token/', AppleView.as_view({'post': 'post'}), name='apple_verify'),
    # path('stream/', stream_chat, name='stream_data'),
    path('forgot-password/', ForgotPasswordView.as_view({'post': 'post'}), name='forgot_password'),
    path("reset-password/", UpdatePasswordView.as_view({'post': 'post'}), name="reset_password"),
    path("goals/tracking/", GoalTrackingView.as_view(), name="goal-tracking"),
    path("me/", me, name="me"),
    path('user/goal/', SelectGoalView.as_view(), name='select_goal'),
    path("user/goal/<uuid:id>/", EditGoalView.as_view(), name="edit_goal"),
    path("user/goal/restore/", RestoreGoalView.as_view(), name="restore_streak"),
    path('user/upload-profile-picture/', ProfilePictureUploadView.as_view(), name='upload-profile-picture'),
    path('feedback/', send_feedback_email, name='feedback'),
    path('notifications/', UserNotificationListView.as_view(), name='user-notifications'),
    path('user/update-fcm-token/', UpdateFCMTokenView.as_view(), name='update-fcm-token'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('reset-chat/', ResetChatView.as_view(), name='reset_chat'),
    path('subscriptions/', SubscriptionPlansView.as_view(), name='subscriptions-list'),
    path('delete-account/', DeleteAccountView.as_view(), name='delete_account'),
    path('deletion-email/', AccountDeletionRequestView.as_view(), name='account_deletion_request'),
    path('stream/', chat_view, name='chat'),
    path('analytics/', AnalyticsView.as_view(), name='analytics'),

    path('webhook/payment-processor/apple/', apple_iap_webhook, name='apple-iap-webhook'),
    path('webhook/payment-processor/google-play/', GooglePlayRTDNWebhook.as_view(), name='google_play_webhook'),

]

urlpatterns += router.urls