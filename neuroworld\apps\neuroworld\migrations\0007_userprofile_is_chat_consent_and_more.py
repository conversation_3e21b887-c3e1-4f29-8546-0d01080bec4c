# Generated by Django 4.2.18 on 2025-03-24 14:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("neuroworld", "0006_userprofile_first_tracked_date"),
    ]

    operations = [
        migrations.AddField(
            model_name="userprofile",
            name="is_chat_consent",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="is_profile_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.Alter<PERSON>ield(
            model_name="userprofile",
            name="first_name",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="userprofile",
            name="gender",
            field=models.Char<PERSON>ield(
                blank=True,
                choices=[("male", "Male"), ("female", "Female"), ("other", "Other")],
                max_length=10,
                null=True,
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="userprofile",
            name="last_name",
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=255, null=True),
        ),
    ]
