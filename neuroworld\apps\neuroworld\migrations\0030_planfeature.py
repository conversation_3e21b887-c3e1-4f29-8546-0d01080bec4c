# Generated by Django 4.2.18 on 2025-05-30 13:41

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('neuroworld', '0029_usersubscription_is_cancelled_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlanFeature',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tier', models.CharField(choices=[('free', 'Free'), ('habits', 'Habits'), ('summit', 'Summit'), ('academy', 'Academy'), ('clinical', 'Clinical')], max_length=20)),
                ('description', models.TextField()),
                ('icon', models.CharField(blank=True, max_length=100, null=True)),
            ],
        ),
    ]
