# Generated by Django 4.2.18 on 2025-03-18 10:20

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('username', models.Char<PERSON>ield(max_length=255, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_staff', models.BooleanField(default=False)),
                ('date_joined', models.DateTimeField(auto_now_add=True)),
                ('auth_provider', models.CharField(choices=[('GOOGLE', 'google'), ('APPLE', 'apple'), ('BASE', 'base')], default='BASE', max_length=10)),
                ('thread_id', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('plan', models.CharField(choices=[('free', 'Free'), ('paid', 'Paid'), ('other', 'Other')], default='free', max_length=50)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Badge',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('image_url', models.URLField()),
            ],
        ),
        migrations.CreateModel(
            name='Goal',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('category', models.CharField(choices=[('nutrition', 'Nutrition'), ('exercise', 'Exercise'), ('unwind', 'Unwind'), ('restore', 'Restore'), ('optimize', 'Optimize')], max_length=50)),
                ('max_limit', models.IntegerField(default=None, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='GoalLevel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('points', models.IntegerField()),
                ('goal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='neuroworld.goal')),
            ],
        ),
        migrations.CreateModel(
            name='GoalVersion',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('description', models.TextField()),
                ('level', models.CharField(choices=[('Beginner', 'BEGINNER'), ('Intermediate', 'INTERMEDIATE'), ('Advanced', 'ADVANCED')], max_length=20)),
                ('goal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='versions', to='neuroworld.goal')),
            ],
        ),
        migrations.CreateModel(
            name='Level',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('rank', models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name='Points',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('action', models.CharField(choices=[('daily_checkin', 'Daily Checkin'), ('extra_checkin_bonus', 'Extra Checkin Bonus'), ('daily_app_open', 'Daily App Open Bonus'), ('weekly_streak_bonus', 'Weekly Streak Bonus'), ('monthly_streak_bonus', 'Monthly Streak Bonus'), ('three_months_streak', 'Three Months Streak Bonus'), ('six_months_streak', 'Six months streak Bonus'), ('one_off_big_actions', 'One Off Big Actions'), ('restore_streak_cost', 'Cost to Restore Streak'), ('assesment_completion', 'Optimize Arcade: Assesment Game Completion'), ('community_engagement_bonus', 'Community Engagement Bonus')], max_length=50)),
                ('points', models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name='UserThread',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('thread_id', models.CharField(max_length=100, unique=True)),
                ('message_count', models.IntegerField(blank=True, default=1, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('first_name', models.CharField(max_length=255)),
                ('last_name', models.CharField(max_length=255)),
                ('points', models.IntegerField(default=0)),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other')], max_length=10)),
                ('is_onboarding_completed', models.BooleanField(default=False)),
                ('badges', models.ManyToManyField(blank=True, to='neuroworld.badge')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserBadge',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('badge', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='neuroworld.badge')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='SelectedGoal',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('start_date', models.DateField(default=None, null=True)),
                ('end_date', models.DateField(default=None, null=True)),
                ('restore_date', models.DateField(default=None, null=True)),
                ('target', models.IntegerField(default=0)),
                ('current_week', models.IntegerField(default=0)),
                ('current_checkins', models.IntegerField(default=0)),
                ('streak', models.IntegerField(default=0)),
                ('streak_awarded', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('goal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='neuroworld.goal')),
                ('goal_level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='neuroworld.goallevel')),
                ('goal_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='neuroworld.goalversion')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='neuroworld.userprofile')),
            ],
        ),
        migrations.CreateModel(
            name='NeuroChat',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('message', models.TextField(blank=True, max_length=10000, null=True)),
                ('role', models.CharField(blank=True, choices=[('user', 'User'), ('assistant', 'Assistant'), ('system', 'System')], default=1, null=True)),
                ('date', models.DateTimeField(auto_now_add=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='GoalTracking',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('date', models.DateField()),
                ('goal_version', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='neuroworld.goalversion')),
                ('selected_goal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='neuroworld.selectedgoal')),
            ],
        ),
        migrations.AddField(
            model_name='goallevel',
            name='level',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='neuroworld.level'),
        ),
        migrations.AddConstraint(
            model_name='goalversion',
            constraint=models.UniqueConstraint(fields=('goal', 'level'), name='unique_goal_intensity'),
        ),
        migrations.AddConstraint(
            model_name='goallevel',
            constraint=models.UniqueConstraint(fields=('goal', 'level'), name='unique_goal_level'),
        ),
    ]
