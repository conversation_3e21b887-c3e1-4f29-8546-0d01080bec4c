from apps.neuroworld.chat_utils.embedder import Embedder
from apps.neuroworld.chat_utils.splitter import Splitter
from apps.neuroworld.services.weaviate_db import WeaviateDB

class StoreVector:
    @classmethod
    def call(cls, db, text, namespace, file_name):
        text_chunks = Splitter.get_text_chunks(text)
        embeddings = Embedder.embedding_func()
        
        # Try calling with just the mandatory arguments
        weaviate_id = db.store(namespace, text_chunks, embeddings)
        return weaviate_id