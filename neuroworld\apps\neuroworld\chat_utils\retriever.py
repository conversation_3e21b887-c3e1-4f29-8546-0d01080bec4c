class Retriever:
  REQUESTED_NUMBER_OF_DOCS_FROM_RETRIEVER = 4

  def get_similar_doc(
    db: any,
    user_query: str,
    namespace: str,
    embeddings: list,
    similarity_score: float,
  ):
    return db.get_similar_docs(
      user_query = user_query,
      namespace = namespace,
      embeddings = embeddings,
      no_of_docs = Retriever.REQUESTED_NUMBER_OF_DOCS_FROM_RETRIEVER,
      similarity_score = similarity_score,
    )
    
    
