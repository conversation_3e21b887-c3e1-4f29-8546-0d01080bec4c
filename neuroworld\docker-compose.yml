version: '3.5'

services:
  web:
    build:
      context: .
      dockerfile: ./Dockerfile
    restart: unless-stopped
    env_file:
      - .env
    networks:
      - integrator-backend-tier
    volumes:
      - .:/code
    ports:
      - "8000:8080"
    depends_on:
      - database

  database:
    image: postgres
    restart: unless-stopped
    volumes:
      - integrator-data-latest:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=neuroworld
    networks:
      - integrator-backend-tier
    ports:
      - "5434:5432"
volumes:
  integrator-data-latest:
    driver: local

networks:
  integrator-backend-tier:
    driver: bridge
