from ast import Sub
from django.contrib import admin
from .models import Goal, GoalVersion, Notification, SelectedGoal, GoalTracking, SubscriptionPlan, UserNotification, UserProfile, User, UserDevice, UserSubscription

admin.site.register(User)
@admin.register(Goal)
class GoalAdmin(admin.ModelAdmin):
    list_display = ("id", "title", "category")
    search_fields = ("title", "category")
    list_filter = ("category",)

@admin.register(GoalVersion)
class GoalVersionAdmin(admin.ModelAdmin):
    list_display = ("id", "goal", "level")
    search_fields = ("goal__title", "level")
    list_filter = ("level", "goal__title")

@admin.register(SelectedGoal)
class SelectedGoalAdmin(admin.ModelAdmin):
    list_display = ("id","get_user_name", "get_goal_title", "get_goal_level", "user", "start_date", "end_date", "streak", "target", "current_week", "current_checkins", "is_active", "streak_awarded", "restore_date")
    search_fields = ("user__name", "goal_version__goal__title")
    list_filter = ("start_date", "end_date")

    def get_goal_title(self, obj):
        return obj.goal_version.goal.title  # Fetch Goal title from GoalVersion
    get_goal_title.short_description = "Goal Title"

    def get_goal_level(self, obj):
        return obj.goal_version.level  # Fetch Level from GoalVersion
    get_goal_level.short_description = "Goal Level"
    
    def get_user_name(self, obj):
        return obj.user.user
    get_user_name.short_description = "User"

@admin.register(GoalTracking)
class GoalTrackingAdmin(admin.ModelAdmin):
    list_display = ("id", "get_user_name", "get_goal_title", "date", "get_goal_version")
    search_fields = ("selected_goal__user__name", "selected_goal__goal_version__goal__title", "goal_version__level" )
    list_filter = ("date", "goal_version")

    def get_goal_version(self, obj):
        return obj.goal_version.level
    get_goal_version.short_description = "Goal Level"

    def get_user_name(self, obj):
        return obj.selected_goal.user.user
    get_user_name.short_description = "User"

    def get_goal_title(self, obj):
        return obj.selected_goal.goal_version.goal.title
    get_goal_title.short_description = "Goal Title"

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ("id", "user", "full_name")
    search_fields = ("name", "user")

@admin.register(UserNotification)
class UserNotificationAdmin(admin.ModelAdmin):
    exclude = ('title', 'body', 'fcm_message_id')  # Hides title and body from the admin form

    list_display = ("id", "user", "get_notification_type", "get_notification_sub_type", "timestamp", "title", "body")
    search_fields = ("user__name", "title", "body", "notification__type", "notification__sub_type")
    list_filter = ("timestamp", "title")

    def get_notification_type(self, obj):
        return obj.notification.type
    get_notification_type.short_description = "Notification Type"

    def get_notification_sub_type(self, obj):
        return obj.notification.sub_type
    get_notification_sub_type.short_description = "Notification Sub Type"

    def save_model(self, request, obj, form, change):
        if not change and obj.notification:
            obj.title = obj.notification.title
            obj.body = obj.notification.message
        super().save_model(request, obj, form, change)

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ("id", "type", "sub_type", "icon", "title", "message")
    search_fields = ("type", "sub_type", "icon", "title", "message")
    list_filter = ("type", "sub_type")

@admin.register(UserDevice)
class UserDeviceAdmin(admin.ModelAdmin):
    list_display = ("id", "user", "fcm_token", "updated_at")
    search_fields = ("user__username", "fcm_token")
    list_filter = ("updated_at",)

@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin):
    list_display = ('name', 'tier', 'billing_period', 'price', 'duration_days')
    list_filter = ('tier', 'billing_period')
    search_fields = ('name', 'product_id')
    readonly_fields = ('id',)

@admin.register(UserSubscription)
class UserSubscriptionsAdmin(admin.ModelAdmin):
    list_display = (
        'user',
        'plan',
        'status',
        'start_time',
        'expiry_time',
        'is_auto_renewing',
        'updated_at',
    )
    list_filter = ('status', 'is_auto_renewing', 'plan__tier', 'plan__billing_period')
    search_fields = ('user__username', 'user__email', 'plan__name', 'purchase_token')
    readonly_fields = ('updated_at',)