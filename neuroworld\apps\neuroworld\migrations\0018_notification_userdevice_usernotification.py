# Generated by Django 4.2.18 on 2025-04-25 12:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('neuroworld', '0017_selectedgoal_first_tracked_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('welcome_onboarding', 'Welcome Onboarding'), ('profile_completion', 'Profile Completion'), ('goal_setting_encouragement', 'Goal Setting Encouragement'), ('check_in_reminder', 'Check In Reminder'), ('target_achievement_celebration', 'Target Achievement Celebration'), ('missed_streak_reminder', 'Missed Streak Reminder'), ('progress_summary', 'Progress Summary'), ('re_engagement', 'Re Engagement'), ('morning_engagements', 'Morning Engagements'), ('midday_support', 'Midday Support'), ('evening_wrap_ups', 'Evening Wrap Ups'), ('weekly_engagements', 'Weekly Engagements'), ('monthly_engagements', 'Monthly Engagements')], max_length=50)),
                ('sub_type', models.CharField(choices=[('welcome_to_neuroworld', 'Welcome To Neuroworld'), ('complete_profile', 'Complete Profile'), ('start_first_goal', 'Start First Goal'), ('check_in_reminder', 'Check In Reminder'), ('goal_achieved', 'Goal Achieved'), ('streak_broken', 'Streak Broken'), ('weekly_recap', 'Weekly Recap'), ('we_miss_you', 'We Miss You'), ('help_seeking_prompt', 'Help Seeking Prompt'), ('daily_motivation', 'Daily Motivation'), ('you_got_this', 'You Got This'), ('check_energy', 'Check Energy'), ('health_tip', 'Health Tip'), ('sunny_day', 'Sunny Day'), ('midday_check', 'Midday Check'), ('stretch_break', 'Stretch Break'), ('snack_time', 'Snack Time'), ('refocus', 'Refocus'), ('breathe', 'Breathe'), ('streak_reminder', 'Streak Reminder'), ('great_job', 'Great Job'), ('plan_tomorrow', 'Plan Tomorrow'), ('wind_down', 'Wind Down'), ('time_to_sleep', 'Time To Sleep'), ('new_week_goals', 'New Week Goals'), ('midweek_checkin', 'Midweek Checkin'), ('rest_reminder', 'Rest Reminder'), ('monthly_update', 'Monthly Update')], max_length=100, unique=True)),
                ('title', models.CharField(max_length=255)),
                ('icon', models.CharField(max_length=100)),
                ('message', models.TextField()),
            ],
        ),
        migrations.CreateModel(
            name='UserDevice',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('fcm_token', models.CharField(max_length=255)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserNotification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('body', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('fcm_message_id', models.CharField(max_length=255)),
                ('notification', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_notifications', to='neuroworld.notification')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', '-timestamp'], name='neuroworld__user_id_b86d68_idx')],
            },
        ),
    ]
