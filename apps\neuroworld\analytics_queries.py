"""
Analytics Query Utilities for NeuroWorld

This module provides utilities for querying data from both PostHog and PostgreSQL
implementing the Query-as-Needed approach. It allows combining event data from
PostHog with structured data from PostgreSQL on-demand.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, date, timedelta
from django.contrib.auth import get_user_model
from django.db.models import Count, Q
import posthog
from django.conf import settings

from .models import UserProfile, SelectedGoal, GoalTracking, User

logger = logging.getLogger(__name__)


class AnalyticsQueryService:
    """
    Service for querying analytics data from both PostHog and PostgreSQL.
    
    Implements the Query-as-Needed approach where data is fetched and combined
    on-demand from both systems.
    """
    
    def __init__(self):
        """Initialize PostHog client for queries."""
        if settings.POSTHOG_ENABLED and settings.POSTHOG_API_KEY:
            posthog.api_key = settings.POSTHOG_API_KEY
            posthog.host = settings.POSTHOG_HOST
            self.enabled = True
        else:
            self.enabled = False
            logger.warning("PostHog analytics disabled - cannot perform queries")
    
    def get_user_events(self, user: User, event_name: str = None, 
                       start_date: date = None, end_date: date = None) -> List[Dict]:
        """
        Get events for a specific user from PostHog.
        
        Args:
            user: Django User instance
            event_name: Optional event name to filter by
            start_date: Optional start date for filtering
            end_date: Optional end date for filtering
            
        Returns:
            List of events from PostHog
        """
        if not self.enabled:
            return []
        
        try:
            # Note: PostHog Python SDK doesn't have direct query capabilities
            # For production, you'd use PostHog's API or HogQL for complex queries
            # This is a placeholder for the structure
            
            user_id = str(user.id)
            
            # In a real implementation, you'd use PostHog's query API
            # For now, we'll return a placeholder structure
            logger.info(f"Querying PostHog events for user {user_id}")
            
            # Placeholder - in production, implement actual PostHog API calls
            return []
            
        except Exception as e:
            logger.error(f"Failed to query PostHog events for user {user.id}: {e}")
            return []
    
    def get_user_analytics_summary(self, user: User, days: int = 30) -> Dict[str, Any]:
        """
        Get a comprehensive analytics summary for a user combining PostHog and PostgreSQL data.
        
        Args:
            user: Django User instance
            days: Number of days to look back
            
        Returns:
            Dictionary containing combined analytics data
        """
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        try:
            # Get PostgreSQL data
            user_profile = UserProfile.objects.get(user=user)
            selected_goals = SelectedGoal.objects.filter(user=user_profile)
            
            # Goal tracking data from PostgreSQL
            goal_tracking = GoalTracking.objects.filter(
                selected_goal__user=user_profile,
                date__gte=start_date,
                date__lte=end_date
            )
            
            # Aggregate PostgreSQL data
            total_goals = selected_goals.count()
            total_checkins = goal_tracking.count()
            unique_tracking_days = goal_tracking.values('date').distinct().count()
            
            # Goal completion rate
            goal_completion_rate = 0
            if total_goals > 0:
                completed_goals = selected_goals.filter(
                    goaltracking__date__gte=start_date
                ).distinct().count()
                goal_completion_rate = (completed_goals / total_goals) * 100
            
            # Combine with PostHog data (placeholder for now)
            posthog_events = self.get_user_events(user, start_date=start_date, end_date=end_date)
            
            return {
                'user_id': str(user.id),
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': days
                },
                'postgresql_data': {
                    'total_goals': total_goals,
                    'total_checkins': total_checkins,
                    'unique_tracking_days': unique_tracking_days,
                    'goal_completion_rate': round(goal_completion_rate, 2),
                    'user_points': user_profile.points,
                    'profile_completed': user_profile.is_profile_completed,
                    'onboarding_completed': user_profile.is_onboarding_completed,
                },
                'posthog_data': {
                    'total_events': len(posthog_events),
                    'events': posthog_events[:10]  # Limit to recent 10 events
                },
                'combined_insights': {
                    'engagement_score': self._calculate_engagement_score(
                        total_checkins, unique_tracking_days, len(posthog_events)
                    ),
                    'activity_trend': self._get_activity_trend(user, start_date, end_date)
                }
            }
            
        except UserProfile.DoesNotExist:
            logger.error(f"UserProfile not found for user {user.id}")
            return {'error': 'User profile not found'}
        except Exception as e:
            logger.error(f"Failed to get analytics summary for user {user.id}: {e}")
            return {'error': 'Failed to retrieve analytics data'}
    
    def get_goal_analytics(self, user: User, goal_id: str = None) -> Dict[str, Any]:
        """
        Get goal-specific analytics combining PostgreSQL and PostHog data.
        
        Args:
            user: Django User instance
            goal_id: Optional specific goal ID to analyze
            
        Returns:
            Dictionary containing goal analytics
        """
        try:
            user_profile = UserProfile.objects.get(user=user)
            
            # Filter goals
            goals_query = SelectedGoal.objects.filter(user=user_profile)
            if goal_id:
                goals_query = goals_query.filter(id=goal_id)
            
            goals_data = []
            for goal in goals_query:
                # PostgreSQL data
                tracking_data = GoalTracking.objects.filter(selected_goal=goal)
                total_checkins = tracking_data.count()
                
                # Calculate streak and consistency
                recent_tracking = tracking_data.order_by('-date')[:7]
                current_streak = self._calculate_current_streak(recent_tracking)
                
                # PostHog data (placeholder)
                goal_events = self.get_user_events(
                    user, 
                    event_name='Goal Tracked'
                )
                
                goals_data.append({
                    'goal_id': str(goal.id),
                    'goal_name': goal.goal.title,
                    'goal_category': goal.goal.category,
                    'target': goal.target,
                    'postgresql_data': {
                        'total_checkins': total_checkins,
                        'current_streak': current_streak,
                        'start_date': goal.start_date.isoformat() if goal.start_date else None,
                        'last_tracking_date': goal.last_tracking_date.isoformat() if goal.last_tracking_date else None,
                    },
                    'posthog_data': {
                        'goal_events': len([e for e in goal_events if goal.goal.title in str(e)]),
                    }
                })
            
            return {
                'user_id': str(user.id),
                'goals': goals_data,
                'summary': {
                    'total_goals': len(goals_data),
                    'total_checkins': sum(g['postgresql_data']['total_checkins'] for g in goals_data),
                    'average_streak': sum(g['postgresql_data']['current_streak'] for g in goals_data) / len(goals_data) if goals_data else 0
                }
            }
            
        except UserProfile.DoesNotExist:
            logger.error(f"UserProfile not found for user {user.id}")
            return {'error': 'User profile not found'}
        except Exception as e:
            logger.error(f"Failed to get goal analytics for user {user.id}: {e}")
            return {'error': 'Failed to retrieve goal analytics'}
    
    def get_user_journey_data(self, user: User) -> Dict[str, Any]:
        """
        Get user journey data combining registration, onboarding, and activity data.
        
        Args:
            user: Django User instance
            
        Returns:
            Dictionary containing user journey data
        """
        try:
            user_profile = UserProfile.objects.get(user=user)
            
            # PostgreSQL journey data
            journey_milestones = {
                'registration_date': user.date_joined.isoformat() if user.date_joined else None,
                'first_goal_selected': None,
                'onboarding_completed': user_profile.is_onboarding_completed,
                'profile_completed': user_profile.is_profile_completed,
                'first_goal_tracking': None,
            }
            
            # Get first goal selection
            first_goal = SelectedGoal.objects.filter(user=user_profile).order_by('id').first()
            if first_goal:
                journey_milestones['first_goal_selected'] = first_goal.id
            
            # Get first goal tracking
            first_tracking = GoalTracking.objects.filter(
                selected_goal__user=user_profile
            ).order_by('date').first()
            if first_tracking:
                journey_milestones['first_goal_tracking'] = first_tracking.date.isoformat()
            
            # PostHog journey events (placeholder)
            journey_events = self.get_user_events(user)
            
            return {
                'user_id': str(user.id),
                'journey_milestones': journey_milestones,
                'postgresql_data': {
                    'total_goals_selected': SelectedGoal.objects.filter(user=user_profile).count(),
                    'total_goal_trackings': GoalTracking.objects.filter(selected_goal__user=user_profile).count(),
                    'current_points': user_profile.points,
                },
                'posthog_data': {
                    'total_events': len(journey_events),
                    'key_events': journey_events[:5]  # Recent key events
                }
            }
            
        except UserProfile.DoesNotExist:
            logger.error(f"UserProfile not found for user {user.id}")
            return {'error': 'User profile not found'}
        except Exception as e:
            logger.error(f"Failed to get user journey data for user {user.id}: {e}")
            return {'error': 'Failed to retrieve user journey data'}
    
    def _calculate_engagement_score(self, checkins: int, active_days: int, events: int) -> float:
        """Calculate a simple engagement score based on activity metrics."""
        # Simple engagement score calculation
        # In production, you'd use more sophisticated algorithms
        base_score = (checkins * 2) + (active_days * 3) + (events * 1)
        return min(base_score / 10, 10.0)  # Normalize to 0-10 scale
    
    def _get_activity_trend(self, user: User, start_date: date, end_date: date) -> str:
        """Get activity trend for the user over the specified period."""
        try:
            user_profile = UserProfile.objects.get(user=user)
            
            # Split period in half to compare
            mid_date = start_date + (end_date - start_date) / 2
            
            first_half = GoalTracking.objects.filter(
                selected_goal__user=user_profile,
                date__gte=start_date,
                date__lt=mid_date
            ).count()
            
            second_half = GoalTracking.objects.filter(
                selected_goal__user=user_profile,
                date__gte=mid_date,
                date__lte=end_date
            ).count()
            
            if second_half > first_half:
                return "increasing"
            elif second_half < first_half:
                return "decreasing"
            else:
                return "stable"
                
        except Exception:
            return "unknown"
    
    def _calculate_current_streak(self, recent_tracking) -> int:
        """Calculate current streak from recent tracking data."""
        if not recent_tracking:
            return 0
        
        streak = 0
        current_date = date.today()
        
        for tracking in recent_tracking:
            if tracking.date == current_date or tracking.date == current_date - timedelta(days=streak):
                streak += 1
                current_date = tracking.date - timedelta(days=1)
            else:
                break
        
        return streak


# Global analytics query service instance
analytics_queries = AnalyticsQueryService()
