from django.conf import settings
from apps.neuroworld.chat_utils.file_handler import <PERSON><PERSON><PERSON><PERSON>
from apps.neuroworld.chat_utils.store_vector import StoreVector
from apps.neuroworld.services.weaviate_db import WeaviateDB
import tempfile
import os
import logging

logger = logging.getLogger(__name__)

def upload_to_weaviate(file_path, file_name, namespace):
    """
    Process a file and upload its contents to Weaviate vector database.
    
    Args:
        file_path (str): Path to the file to be processed
        file_name (str): Original name of the file
        namespace (str): Weaviate namespace to store the vectors
        
    Returns:
        str: Weaviate ID of the uploaded document
    """
    try:
        vector_db = WeaviateDB()
        text = FileHandler.get_text(file_path=file_path, file_name=file_name)
        
        weaviate_id = StoreVector.call(
            db=vector_db,
            text=text,
            namespace=namespace,
            file_name=file_name,
        )
        
        return weaviate_id
    except Exception as e:
        logger.error(f"Failed to upload to Weaviate: {str(e)}")
        raise