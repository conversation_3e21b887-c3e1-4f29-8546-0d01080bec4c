from enum import Enum

class UserRoleEnum(Enum):
  admin = "admin"
  member = "member"

class GoalCategory(Enum):
  NUTRITION = ("nutrition", "Nutrition")
  EXERCISE = ("exercise", "Exercise")
  UNWIND = ("unwind", "Unwind")
  RESTORE = ("restore", "Restore")
  OPTIMIZE = ("optimize", "Optimize")

  def __new__(cls, value, label):
    obj = object.__new__(cls)
    obj._value_ = value
    obj.label = label
    return obj
  
  @classmethod
  def from_value(cls, value):
    for member in cls:
      if member.value == value:
        return member
    raise ValueError(f"No matching GoalIntensity for value: {value}")

class GoalIntensity(Enum):
  BEGINNER = ("Beginner", "BEGINNER")
  ADVANCED = ("Advanced", "ADVANCED")
  
  def __new__(cls, value, label):
    obj = object.__new__(cls)
    obj._value_ = value
    obj.label = label
    return obj
  
  @classmethod
  def from_value(cls, value):
    for member in cls:
      if member.value == value:
        return member
    raise ValueError(f"No matching GoalIntensity for value: {value}")
