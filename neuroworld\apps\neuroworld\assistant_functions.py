from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Any, Dict, List

from .serializers import UserProfileSerializer, UserSerializer

from .utils import user_has_goal_in_category

from .constants import ASSISTANT_GET_GOAL_VERSION_INSTRUCTIONS, ASSISTANT_GET_GOALS_INSTRUCTIONS, ASSISTANT_GET_USER_GOALS_INSTRUCTIONS, ASSISTANT_SELECT_GOAL_INSTRUCTIONS, FREE_USER_PER_ISLAND_ONE_GOAL_LIMIT
from .models import Goal, GoalVersion, PlanTier, Points, SelectedGoal, User, UserProfile
from ..cms.models import Character
from enum import Enum
from typing import List, Dict
import json
from django.core.exceptions import ObjectDoesNotExist

def get_user_goals(user: User) -> Dict[str, List[Dict[str, str]]]:
    """
    Get all goals selected by the user.

    Args:
        user (User): The user whose goals are to be retrieved.

    Returns:
        dict: A JSON-serializable dictionary containing a list of goals, 
              where each goal has a UUID, description, start date, and end date.
    """
    try:
        # Fetch selected goals with necessary relations
        user_profile = UserProfile.objects.get(user=user)
        selected_goals = SelectedGoal.objects.filter(user=user_profile)

        # Build the response data
        response = {
            "description" : ASSISTANT_GET_USER_GOALS_INSTRUCTIONS,
            "goals": [
                {
                    "uuid": str(goal.goal_version.id),
                    "name": goal.goal.title,
                    "description": goal.goal_version.description,
                    "streak": goal.streak,
                    "current_week": goal.current_week,
                    "current_checkins": goal.current_checkins,
                }
                for goal in selected_goals
            ]
        }

        # Serialize the response to a JSON string
        return json.loads(json.dumps(response))  # Ensure it's JSON serializable
    except ObjectDoesNotExist:
        return {"error": "No goals found for the user."}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}


def get_goals(user : UserProfile, category : str = None) -> Dict[str, List[Dict[str, str]]]:
    """
    Fetches goals from the database and returns them in the required structure.

    Returns:
        dict: A dictionary containing a list of goals, where each goal has a UUID and description.
    """
    user_profile = UserProfile.objects.filter(user=user).first()
    try:
        #check if user has a free plan and has a goal in this category
        if user_has_goal_in_category(user_profile, category):
            return {"description": FREE_USER_PER_ISLAND_ONE_GOAL_LIMIT}
        # Query the database to fetch all goals
        if category:
            goals = Goal.objects.filter(category=category)
        else:
            goals = Goal.objects.all()
        
        # Convert the QuerySet into a list of dictionaries
        goals_list = []
        for goal in goals:
            goal_dict = {
                "goal": {
                    "uuid": str(goal.id),  # Ensure UUID is converted to string
                    "description": goal.title,
                }
            }
            goals_list.append(goal_dict)

        # Create the response dictionary
        response = {
            "text": ASSISTANT_GET_GOALS_INSTRUCTIONS,
            "avatar": None,
            "actionType": "LIST",
            "actions": goals_list,  # Use the serialized list of goals
        }

        return json.loads(json.dumps(response))

    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}
        

    except Exception as e:
        # Handle any errors that might occur
        return {"error": f"An error occurred: {str(e)}"}
    
def get_goal_version(goal_id: str) -> Dict:
    """
    Fetches goal versions for the given goal ID and returns them in the required structure.

    Args:
        goal_id (str): The UUID of the selected goal.

    Returns:
        dict: A dictionary containing goal versions with different difficulty levels.
    """
    try:
        # Query the database to fetch goal versions for the selected goal
        goal_versions = GoalVersion.objects.filter(goal_id=goal_id)

        # Convert the QuerySet into a list of formatted actions with level as a key
        actions = [
            {
                "level": version.level.upper(),  # Ensure level is in uppercase
                "id": str(version.id),
                "text": version.description
            }
            for version in goal_versions
        ]

        # Determine the avatar based on the goal's category (assuming goal has a category)
        goal = Goal.objects.get(id=goal_id)
        avatar = getattr(Avatar, goal.category, Avatar.Myla)  # Default to Myla if no match

        # Create and return the response
        return create_response(
            text=ASSISTANT_GET_GOAL_VERSION_INSTRUCTIONS,
            action_type=ActionType.List,
            actions=actions,
            avatar=avatar
        )

    except Goal.DoesNotExist:
        return {"error": "The specified goal does not exist."}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}

def finalize_goal(user: User, goal_version_id: str, week_target: int ,start_date: str, end_date: str) -> Dict[str, str]:
    """
    Select a goal for the user.

    Args:
        user (User): The user who is selecting the goal.
        goal_id (str): The ID of the goal to be selected.
        start_date (str): The start date of the goal in "YYYY-MM-DD" format.
        end_date (str): The end date of the goal in "YYYY-MM-DD" format.

    Returns:
        dict: A dictionary containing the details of the selected goal and the status of the operation.
    """
    try:
        # Check if the user has a profile
        user_profile = UserProfile.objects.filter(user=user).first()
        if not user_profile:
            return {
                "error": "User profile not found. Please create a profile before selecting goals.",
                "status": False
            }
        
        # Check if the user has already selected a goal in the same category
        if user.subscription_tier == PlanTier.FREE:
            if user_has_goal_in_category(user_profile, goal_version_id):
                return {
                    "error": "You are on Free plan and can only add one goal from each category. You have already selected a goal in this category.",
                    "status": True
                }
        

        # Retrieve the goal for now get the initial goal version
        goal = Goal.objects.get(id=goal_version_id)
        goal_version = GoalVersion.objects.get(goal_id=goal_version_id, level="Beginner")
        
        # same goal version can not be selected again
        if SelectedGoal.objects.filter(user=user_profile, goal=goal).exists():
            # we can update the selected goal with week_target and respond goal selected with selected week target
            selected_goal = SelectedGoal.objects.get(user=user_profile, goal=goal)
            selected_goal.target = week_target
            selected_goal.save()
            # Build response                
            return {
                "detail": "Selected goal is finalized with the selected week target. send 'actionType' as 'CTA' and 'action' ['View Goal'].",
                "goal": {
                    "uuid": str(selected_goal.id),
                    "description": selected_goal.goal_version.description,
                    "week_target": selected_goal.target,},
                "status": True
            }
        
        # Create the selected goal
        selected_goal = SelectedGoal.objects.create(
                    user=user_profile,
                    goal_version=goal_version,
                    goal=goal,
                    target=week_target
                )
        # Build response
        response = {
            "description" : ASSISTANT_SELECT_GOAL_INSTRUCTIONS,
            "success": True,
            "selected_goal" : {"uuid": str(selected_goal.id),
            "description": selected_goal.goal_version.description,
            "status": True}
        }

        # we will complete the onboarding process if user is not completed yet.
        if not user_profile.is_onboarding_completed:
            user_profile.is_onboarding_completed = True
            user_profile.save()
            response["onboarding_completed"] = True 

        return response

    except Goal.DoesNotExist:
        return {"error": "Goal not found", "status": False}
    except ValueError as e:
        return {"error": f"Invalid date format: {str(e)}", "status": False}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}", "status": False}

def delete_goal(user: User, goal_id: str) -> Dict[str, Any]:
    """
    Delete a selected goal for the user.

    Args:
        user (User): The user whose goal is to be deleted.
        goal_id (str): The ID of the goal to be deleted.

    Returns:
        dict: A dictionary containing the status of the operation and a message.
    """
    try:
        # Fetch the user's selected goal
        selected_goal = SelectedGoal.objects.get(user__user=user, goal_version=goal_id)
        
        # Delete the goal
        selected_goal.delete()
        
        response = {
            "status": True,
            "message": f"Goal with ID {goal_id} successfully deleted.",
            "user_goals": get_user_goals(user)
        }
        return response

    except SelectedGoal.DoesNotExist:
        return {
            "status": False,
            "error": f"No selected goal found with ID {goal_id} for the given user."
        }
    except Exception as e:
        return {
            "status": False,
            "error": f"An error occurred: {str(e)}"
        }


class Avatar(Enum):
    Myla = "Myla"
    Nuri = "Nuri"
    Spark = "Spark"
    Flo = "Flo"
    Luna = "Luna"
    Sophi = "Sophi"

class ActionType(Enum):
    List = "LIST"
    Scale = "SCALE"
    Button = "BUTTON"

def create_response(text: str, action_type: ActionType, actions: List[Dict], avatar: Avatar= None) -> Dict:
    return {
        "text": text,
        "avatar": avatar.value if avatar else None,
        "actionType": action_type.value,
        "actions": actions
    }


# Character Profiles
# CHARACTER_PROFILES = {
#     "Myla": {
#         "role": "The Brain and Guide of NEURO World",
#         "tone": "Wise, insightful, and slightly humorous",
#         "background": "Born from NEURO’s core, Myla is the architect of change, embodying cognitive mastery, emotional resilience, and strategic thinking. She inspires all characters to turn limitations into strengths through the Ripple Effect.",
#         "few_shots": [
#             "Big changes start with tiny ripples—so splash wisely!",
#             "Don’t stress the mess; even brilliance needs a little chaos to grow.",
#             "Your brain isn’t a machine—it’s a garden. Tend it with care and patience."
#         ]
#     },
#     "Nuri": {
#         "role": "The Gorilla of Nutrition",
#         "tone": "Encouraging, passionate about plant-based nutrition, and nurturing, always try to motivate or push users to eat a plant-based diet, no direct meat suggestions, but if user really wants to eat it, try adding fish first or you in your own amazing way help them with it.",
#         "background": "Nuri, a strong yet gentle giant, is passionate about nourishment and sustainability. He loves cooking, foraging, and educating others on brain-healthy food choices.",
#         "few_shots": [
#             "I’m proof that plants make you strong—just look at these biceps!",
#             "Eat the rainbow, fuel your brain—because gray matter loves color!",
#             "Real strength isn’t just what you lift—it’s what you choose to put on your plate."
#         ]
#     },
#     "Spark": {
#         "role": "The Sloth of Exercise",
#         "tone": "Playful, motivational, and relatable",
#         "background": "Despite being a sloth, Spark is the most dedicated athlete in NEURO World, celebrating small fitness victories and encouraging others to embrace movement at their own pace.",
#         "few_shots": [
#             "One step at a time... but hey, look at me sprinting in spirit!",
#             "Slow and sweaty wins the race—eventually!",
#             "If sloths can have abs, you can take one more step!"
#         ]
#     },
#     "Flo": {
#         "role": "The Swan of Stress Management",
#         "tone": "Calm, reflective, and slightly dramatic",
#         "background": "Flo symbolizes grace under pressure but often struggles with stress. She practices mindfulness and encourages others to navigate stress through intentional pauses and breathing techniques.",
#         "few_shots": [
#             "Keep calm and paddle furiously—no one needs to know!",
#             "Breathe in serenity... breathe out the chaos—preferably far away from me!",
#             "Finding balance isn’t about avoiding waves—it’s about learning to ride them."
#         ]
#     },
#     "Luna": {
#         "role": "The Owl of Restorative Sleep",
#         "tone": "Serious about sleep, structured, and humorous",
#         "background": "Luna ensures everyone in NEURO World prioritizes good sleep. She is obsessed with optimizing rest, creating the perfect sleep environment, and emphasizing the importance of rest for cognitive function.",
#         "few_shots": [
#             "Sleep is my superpower—what’s yours?",
#             "Rest isn’t lazy—it’s a productivity hack. Try it, you’ll thank me in the morning.",
#             "Good sleep isn’t a luxury—it’s the foundation of everything you want to achieve."
#         ]
#     },
#     "Sophi": {
#         "role": "The Octopus of Cognitive Activity",
#         "tone": "Witty, intelligent, and slightly scattered",
#         "background": "Sophi is a brilliant but chaotic thinker who has learned to tame multitasking through focus and organization. She guides others in structuring their thoughts and embracing curiosity.",
#         "few_shots": [
#             "I may have eight arms, but I’m finally learning to use two at a time!",
#             "Multitasking? More like multi-asking for help—let’s get organized!",
#             "Brains over brawn—unless it’s Nuri, then I defer to the kale!"
#         ]
#     }
# }


def get_character_profiles():
    """
    Fetches all Character entries from the database and returns them in the CHARACTER_PROFILES format.
    """
    character_profiles = {}

    characters = Character.objects.all()
    for character in characters:
        character_profiles[character.name] = {
            "role": character.role,
            "tone": character.tone,
            "background": character.background,
            "few_shots": character.few_shots
        }

    return character_profiles

# Function to introduce a character with detailed personalization
def introduce_character(character_name: str) -> Dict[str, str]:
    """
    Introduce a character with the given name, providing background, tone, and example phrases.

    Args:
        character_name (str): The name of the character to introduce.

    Returns:
        dict: A dictionary containing the character's role, tone, background, and sample dialogue.
    """

    CHARACTER_PROFILES = get_character_profiles()
    if character_name in CHARACTER_PROFILES:
        character = CHARACTER_PROFILES[character_name]
        return {
            "name": character_name,
            "role": character["role"],
            "tone": character["tone"],
            "background": character["background"],
            "few_shots": character["few_shots"]
        }
    else:
        return {
            "name": character_name,
            "description": f"Character '{character_name}'. Please ensure the character aligns with it's unique personas and styles, emphasizing their role, tone, and background."
        }
    
# Function to get userProfile and User object
def get_user_profile(user: str) -> Dict[str, Any]:
    """
    Fetches the UserProfile and User object for the given user ID.

    Args:
        user_id (str): The ID of the user.

    Returns:
        dict: A dictionary containing the UserProfile and User object.
    """
    try:
        user = user
        user_profile = UserProfile.objects.get(user=user)
        return {
            "user": UserSerializer(user).data,
            "user_profile": UserProfileSerializer(user_profile).data
        }
    except User.DoesNotExist:
        return {"error": "User not found."}
    except UserProfile.DoesNotExist:
        return {"error": "User profile not found."}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}
    

def get_user_streaks_and_points(user: str) -> Dict[str, Any]:
    """
    Retrieve points and streak details for the user, including streaks and points for each goal.

    Args:
        user_id (str): Unique identifier for the user.
        goals (List[Dict[str, Any]]): List of goals with streak details.

    Returns:
        dict: A dictionary containing the user's streaks and points details for each goal.
    """
    try:
        # Fetch the user and their profile
        user_profile = UserProfile.objects.get(user=user)
        # Fetch sleected goals for the user
        selected_goals = SelectedGoal.objects.filter(user=user_profile)
        # Prepare the response structure
        response = {
            "user_id": str(user.id),
            "user_name": user_profile.full_name,
            "streaks_and_points": []
        }
        # Iterate through each selected goal and fetch streaks and points
        for selected_goal in selected_goals:
            goal = selected_goal.goal
            # Assuming each goal has a streak and points attribute
            streak = selected_goal.streak
            response["streaks_and_points"].append({
                "goal_id": str(goal.id),
                "goal_title": goal.title,
                "streak": streak,
                "weekly_target": selected_goal.target,
                "weekly_checkins": selected_goal.current_checkins,
            })

        response["user_profile"] = UserProfileSerializer(user_profile).data
        response["user"] = UserSerializer(user).data
        response["instructions"] = "Remember Streak is not days, It's the number of weeks you have completed the goal, with your frequency"
        return response

    except User.DoesNotExist:
        return {"error": "User not found."}
    except UserProfile.DoesNotExist:
        return {"error": "User profile not found."}
    except Goal.DoesNotExist:
        return {"error": "One or more goals not found."}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}
    
def get_points_information():
    """
    Fetches all points information from the Points model.

    Returns:
        dict: A dictionary containing points information.
    """
    try:
        # Fetch all points records from the Points model
        points = Points.objects.all()
        # Prepare points data
        points_data = []
        for point in points:
            points_data.append({
                "id": str(point.id),
                "points": point.points,
                "action": point.action,
            })
        return {"points": points_data}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}
        