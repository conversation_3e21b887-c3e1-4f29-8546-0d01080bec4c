from datetime import timed<PERSON>ta
import logging
import time
from typing import Dict, List
from django.conf import settings
import firebase_admin
from firebase_admin import credentials, messaging
from firebase_admin import exceptions
from asgiref.sync import sync_to_async
from ..models import UserDevice

from google.auth.transport.requests import AuthorizedSession
from google.auth import default
from requests.adapters import HTTPAdapter

logger = logging.getLogger(__name__)
# Initialize Firebase Admin SDK

cred = credentials.Certificate(settings.GOOGLE_APPLICATION_CREDENTIALS)

firebase_app = firebase_admin.initialize_app(cred)

logger = logging.getLogger(__name__)


# --- Setup a shared AuthorizedSession with increased connection pool size ---
credentials_, _ = default()
authed_session = AuthorizedSession(credentials_)
adapter = HTTPAdapter(pool_connections=100, pool_maxsize=100)
authed_session.mount("https://", adapter)

# Patch Firebase SDK's internal session to reuse connections efficiently
messaging._session = authed_session



# https://firebase.google.com/docs/reference/admin/python/firebase_admin.messaging

# Firebase messaging functions
def _handle_fcm_error(exception, tokens: str):
    """
    Helper function to handle Firebase Cloud Messaging errors.
    Logs the error and raises appropriate custom exception.
    """
    if isinstance(exception, messaging.SenderIdMismatchError):
        logger.error(f"Sender ID mismatch for tokens: {tokens}")
        raise messaging.SenderIdMismatchError(f"Sender ID mismatch for tokens: {tokens}") from exception
    elif isinstance(exception, messaging.QuotaExceededError):
        logger.error("Quota exceeded. Retry later.")
        raise messaging.QuotaExceededError("Quota exceeded. Retry later.") from exception
    elif isinstance(exception, messaging.ThirdPartyAuthError):
        logger.error("Third-party authentication failed.")
        raise messaging.ThirdPartyAuthError("Third-party authentication failed.") from exception
    elif isinstance(exception, messaging.UnregisteredError):
        logger.error(f"Unregistered or expired tokens: {tokens}")
        raise messaging.UnregisteredError(f"Unregistered or expired tokens: {tokens}") from exception
    elif isinstance(exception, exceptions.FirebaseError):
        logger.error(f"Firebase error: {exception}")
        raise exceptions.FirebaseError(f"Firebase error: {exception}") from exception
    elif isinstance(exception, exceptions.ValueError):
        logger.error(f"Value error: {exception}")
        raise exceptions.ValueError(f"Value error: {exception}") from exception
    else:
        logger.error(f"Unknown error: {exception}")
        raise exception

def send_silent_notification_to_user(token: str, data: Dict[str, str]) -> str:
    """
    Send a silent push notification to a user using Firebase Cloud Messaging.

    Args:
        token (str): The FCM token of the user to send the notification to.
        data (dict): Notification as data for silent push.

    Returns:
        str: Response from Firebase Cloud Messaging.
    """
    # Silent push notifications only use the 'data' field
    message = messaging.Message(
        data=data,
        token=token,
    )

    try:
        # Send the message via Firebase Cloud Messaging
        response = messaging.send(message)
        logger.info(f"Silent notification sent successfully to token: {token}")
        return response
    except Exception as e:
        _handle_fcm_error(e, token)

def send_multicast_notifications(tokens: List[str], title: str, body: str, image: str, data: Dict[str, str] = {}) -> str:
    """
    Send a notification to multiple users using Firebase Cloud Messaging.

    Args:
        tokens (list): The FCM tokens of the users to send the notification to.
        title (str): The title of the notification.
        body (str): The body of the notification.
        image (str): The URL of the image to display in the notification.
        data (dict): Additional data to send with the notification. (optional)

    Returns:
        str: Response from Firebase Cloud Messaging.
    """
    # Multicast message with notification data
    message = messaging.MulticastMessage(
        notification=messaging.Notification(
            title=title,
            body=body,
            image=image,
        ),
        data=data,
        tokens=tokens,
        android=messaging.AndroidConfig(
            ttl=int((timedelta(days=2).total_seconds())),  # TTL set to 2 days
            priority='normal',
        ),
        apns=messaging.APNSConfig(
            headers={
                'apns-expiration': str(int((timedelta(days=2).total_seconds()))),  # TTL set to 2 days in seconds since epoch
            },
        ),
    )

    try:
        # Send multicast message
        response = messaging.send_each_for_multicast(message)
        return response
    except Exception as e:
        _handle_fcm_error(e, tokens)

async def fcm_send_with_retry(user_messages, base_notification):
    """
    Send notifications to multiple users with retry logic for failed tokens.
    Args:
        user_messages (list): List of dictionaries containing user_id, tokens, notification_id, and message.
        base_notification (dict): Base notification data containing type and other metadata.
    Returns:
        tuple: A tuple containing two lists - successful and failed notifications.
    """
    max_retries = 3
    initial_delay = 5  # seconds

    data = {'type': base_notification.type, 'route_location': "/notification"}

    successful = []
    failed = []

    for user_msg in user_messages:
        user_id = user_msg['user_id']
        tokens = user_msg['tokens']
        message_text = user_msg['message']
        notification_id = user_msg.get('notification_id', None)

        # Tokens to retry
        tokens_to_send = tokens.copy()

        for attempt in range(max_retries):
            if not tokens_to_send:
                break

            logger.info(f"Attempt [{attempt + 1}/{max_retries}] for user {user_id} tokens: {tokens_to_send}")

            response = send_multicast_notifications(tokens_to_send, title=base_notification.title, body=message_text, image=base_notification.icon, data=data)

            if response is None:
                # On complete failure, retry whole batch after backoff
                sleep_time = initial_delay * (2 ** attempt)
                logger.info(f"Waiting {sleep_time}s before retry for user {user_id}...")
                time.sleep(sleep_time)
                continue

            next_retry_tokens = []

            for idx, res in enumerate(response.responses):
                token = tokens_to_send[idx]

                if res.success:
                    successful.append({
                        'user_id': user_id,
                        'token': token,
                        'message': message_text,
                        'message_id': res.message_id,
                        'notification_id': notification_id
                    })
                else:
                    logger.warning(f"Token {token} failed: {res.exception} - {res.exception.code}")

                    if res.exception.code == exceptions.NOT_FOUND or res.exception.code == exceptions.INVALID_ARGUMENT:
                        logger.info(f"Removing invalid token: {token}")
                        await sync_to_async(
                            lambda token=token: UserDevice.objects.filter(user_id=user_id, fcm_token=token).delete()
                        )()
                    else:
                        next_retry_tokens.append(token)

            tokens_to_send = next_retry_tokens

            if tokens_to_send and attempt < max_retries - 1:
                sleep_time = initial_delay * (2 ** attempt)
                logger.info(f"Waiting {sleep_time}s before next retry for user {user_id}...")
                time.sleep(sleep_time)

        # After retries, if still failed tokens, record them
        for token in tokens_to_send:
            failed.append({'user_id': user_id, 'token': token, 'error': 'Failed after retries'})

    return successful, failed
