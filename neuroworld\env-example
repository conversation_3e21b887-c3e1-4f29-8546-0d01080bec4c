#Add example env here
DJANGO_DEBUG=False
DATABASE_URL=""

DJANGO_SECRET_KEY=""
DJANGO_ADMIN_URL=""
DJANGO_ALLOWED_HOSTS=""

AUTH_APPLE_KEY_ID=""
AUTH_APPLE_TEAM_ID=""
AUTH_APPLE_PRIVATE_KEY=""
AUTH_APPLE_CLIENT_ID=""
AUTH_APPLE_APP_ID=""
AUTH_APPLE_ALGORITHM=""
ACCESS_TOKEN_URL=''

OPENAI_API_KEY=""
ASSISTANT_ID=""
OPEN_AI_KEY=""
CORS_ORIGIN_ALLOW_ALL = True
CSRF_TRUSTED_ORIGINS = ['localhost:3000']
CORS_ORIGIN_WHITELIST=["http://localhost:3000"]

EMAIL_HOST=
EMAIL_PORT=
DEFAULT_FROM_EMAIL=
EMAIL_BACKEND=
USE_AWS_SES=False
RESET_PASSWORD_DEEP_LINK=
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
EMAIL_USE_SSL=True

#Set true in dev/qa servers and false in staging/prod servers
USE_S3_SECRETS=False

#Only needed if USE_S3_SECRETS is set to true
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""

AWS_S3_REGION_NAME=""
AWS_STORAGE_BUCKET_NAME=""
AWS_QUERYSTRING_EXPIRE=3600
DEFAULT_FILE_STORAGE=""

SUPPORT_MAIL_RECIPIENT=""

GOOGLE_APPLICATION_CREDENTIALS=
MESSAGE_COUNT_THRESHOLD=

AWS_SQS_REGION=
AWS_SQS_URL=

ENVIRONMENT_NAME=dev

# PostHog Analytics
POSTHOG_API_KEY=phc_your_api_key_here
POSTHOG_HOST=https://app.posthog.com
POSTHOG_ENABLED=True