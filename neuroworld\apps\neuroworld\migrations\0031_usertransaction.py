# Generated by Django 4.2.18 on 2025-06-02 08:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('neuroworld', '0030_planfeature'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserTransaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('platform', models.CharField(choices=[('google', 'Google Play'), ('apple', 'Apple App Store')], max_length=20)),
                ('transaction_id', models.CharField(max_length=255, unique=True)),
                ('original_transaction_id', models.CharField(blank=True, max_length=255, null=True)),
                ('product_id', models.CharField(max_length=255)),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('currency', models.CharField(default='USD', max_length=10)),
                ('transaction_type', models.CharField(choices=[('initial_purchase', 'Initial Purchase'), ('renewal', 'Renewal'), ('cancel', 'Cancel'), ('refund', 'Refund'), ('revoke', 'Revoke')], max_length=50)),
                ('transaction_time', models.DateTimeField()),
                ('raw_data', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('subscription', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='neuroworld.usersubscription')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
