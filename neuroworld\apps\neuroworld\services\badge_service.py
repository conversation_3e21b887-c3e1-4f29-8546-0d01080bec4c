# users/services/badge_service.py
from django.db import transaction
from django.utils import timezone
from datetime import timedelta
from apps.neuroworld.models import GoalCategory, SelectedGoal, GoalTracking, Badge, UserBadge, BadgeType, BadgeLevel

class BadgeService:
    
    @classmethod
    def check_streak_badges(cls, user):
        """Check and award streak badges for a user"""
        profile = user.userprofile
        categories = GoalCategory.choices
        badges_awarded = []
        for category in categories:
            category_value, _ = category
            # Get all active goals in this category
            goals = SelectedGoal.objects.filter(
                user=profile,
                goal__category=category_value,
                is_active=True
            )
            
            if not goals.exists():
                continue
                
            # Check highest streak in this category
            max_streak = max(g.streak for g in goals)
            min_streak = min(g.streak for g in goals)

            # Check how many habits have at least the minimum streak for Diamond/Platinum
            qualified_habits = sum(
                1 for g in goals 
                if g.streak >= 36  # Minimum for Diamond
            )
            
            # Check which badges to award
            badges_to_check = Badge.objects.filter(
                badge_type=BadgeType.STREAK,
                category=category_value
            ).order_by('required_streak')
            
            for badge in badges_to_check:
                # Skip if user already has this badge
                if UserBadge.objects.filter(user=user, badge=badge).exists():
                    continue
                print(f"Checking badge: {badge.title} for user")
                # Check requirements
                if badge.level in [BadgeLevel.PLATINUM]:
                    meets_requirement = qualified_habits >= 3 and min_streak >= badge.required_streak
                elif badge.level in [BadgeLevel.DIAMOND]:
                    meets_requirement = qualified_habits >= 3 and max_streak >= badge.required_streak
                else:
                    meets_requirement = max_streak >= badge.required_streak
                    
                if meets_requirement:
                    badge = cls.award_badge(user, badge)
                    badges_awarded.append(badge)
        return badges_awarded
    
    #TODO: Implement city growth badges

    @classmethod
    def award_badge(cls, user, badge):
        """Award a badge to a user"""
        with transaction.atomic():
            user_badge, created = UserBadge.objects.get_or_create(
                user=user,
                badge=badge,
                defaults={'earned_at': timezone.now()}
            )
            
            if created:
                # Add to user's profile badges
                user.userprofile.badges.add(badge)
                
                # Send notification
                
        return user_badge
    
    #TODO: Implement notification logic