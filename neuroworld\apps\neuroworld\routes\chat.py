# from fastapi                  import APIRouter, Depends
# from sqlalchemy.orm           import Session
# from app.database.db          import get_db
# from chat_utils.schemas        import ChatQnA
# from app.database.migrations  import User as UserDB
from services.weaviate_db import WeaviateDB
# from fastapi.responses import StreamingResponse
from interactors import chat as chat_interactor

# router = APIRouter(prefix="/api", tags=['Chat'])

# @router.get('/get-chat-history')
# def get_search_filters(
#   db:           Session = Depends(get_db),
#   current_user: UserDB = Depends(AuthToken.validate),
# ):
#   return list_chat_history_interactor.call(db, current_user)

# @router.post('/chat')
# async def chat(request: ChatQnA, db: Session=Depends(get_db)):
#   # return await chat_interactor.call(db=db, request=request, vector_db = WeaviateDB, namespace='cd-1')
#   return StreamingResponse(chat_interactor.call(db=db, request=request, vector_db = WeaviateDB, namespace='cd-1'), media_type="text/plain")
