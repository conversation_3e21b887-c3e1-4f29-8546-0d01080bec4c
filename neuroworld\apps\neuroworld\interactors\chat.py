import ast
import json
import asyncio
from datetime import datetime

from django.http import Http404

from ..utils import save_message_to_db
from ..services.llm import LLM
from ..chat_utils.prompt_hub import PromptHub, AFFIRMATIVE_RESPONSES
from ..chat_utils.retriever import Retriever
from ..chat_utils.embedder import Embedder
from ..chat_utils.question_generator import QuestionGenerator
from ..models import ActionType, Chat, NeuroChat, Points, User, SelectedGoal, UserProfile
from ..graph.builder import graph_builder
from ..management.commands.seed_subscriptions import PLAN_FEATURES
from asgiref.sync import sync_to_async

import json
import uuid
from datetime import datetime
from django.utils.timezone import now
import random

async def call(db, request, vector_db, namespace, query):
    user = request.user.id
    
    #Save user Query to the database
    await save_message_to_db(
        user=request.user,
        user_message=query,
    )
    
    try:
        chat = await sync_to_async(Chat.objects.get)(user=user)
    except Chat.DoesNotExist:
        chat = await sync_to_async(Chat.objects.create)(
            user=request.user,
            attrs={'history': [], 'action_in_progress': None, 'executed_nodes': []}
        )
    
    history = chat.attrs.get('history', [])
    num = -3 if len(history) >= 3 else -1 * len(history)
    conversation_history = chat.attrs['history'][num:] if num != 0 else 'No History'
    
    if conversation_history != 'No History':
        new_question = QuestionGenerator.call(history = history, current_question = query)
    else:
        new_question = conversation_history

    if not chat.attrs['action_in_progress']:
        greeting_messages = PromptHub.greeting_prompt(query)
        character_selector_messages = PromptHub.character_selecter(query, conversation_history)

        greeting_task = asyncio.create_task(LLM.async_ask_from_gpt(messages = greeting_messages, model_name = 'gpt-4o', key='greeting_result'))
        character_task = asyncio.create_task(LLM.async_ask_from_gpt(messages = character_selector_messages, model_name = 'gpt-4o', key='character_result'))
        
        loop = asyncio.get_running_loop()
        vector_db_task = loop.run_in_executor(None, get_similar_data, vector_db, namespace, request, 'vector_db_result', new_question)
        key, greeting_answer = await greeting_task

    executed_nodes = chat.attrs['executed_nodes']

    if  chat.attrs['action_in_progress'] or 'ADD GOAL' in greeting_answer:
        llm_last_message = await sync_to_async(NeuroChat.objects.filter(user=request.user, role="assistant").order_by('-date').first)()
        
        if llm_last_message:
            llm_last_message = ast.literal_eval(llm_last_message.message) #convert string to dict
        
        response = await graph_builder.ainvoke({
            'db': db,
            'chat': chat,
            'character': 'Myla',
            'user_query': query,
            'executed_nodes': executed_nodes,
            'action_in_progress': "add_goal",
            'conversation_history': conversation_history,
            'llm_last_message': llm_last_message, 
        })
        attrs = chat.attrs
        attrs["action_in_progress"] = response['action_in_progress']
        attrs["executed_nodes"] = response["executed_nodes"]
        await sync_to_async(chat.save)(update_fields=['attrs'])

        index = 0
        if len(response['ai_reply']) > 1:
            index = 1
            data = {"user": str(user), "character": response['ai_reply'][0]['avatar']}
            # yield json.dumps(data)
            await asyncio.sleep(0.1)
            answer = ""
            end_time = None
            for i in response['ai_reply'][0]['text'].split(' '):
                if end_time == None:
                    end_time = datetime.now()
                answer += i + ' '
                yield {"chunk": i + ' ', "last_message": False}
                await asyncio.sleep(0.1)
            final_response = await save_message_to_db(
                user=request.user,
                assistant_message=answer,
                character=response['character'],
                action_type=response['ai_reply'][0]['actionType'],
                actions=response['ai_reply'][0]['actions']
            )
            last_chunk = {
                "chunk": final_response,
                "last_message": True
            }
            yield last_chunk            

        res = {
            'answer': response['ai_reply'][index]['text'],
            'character': response['ai_reply'][index]['avatar'],
            'action_type': response['ai_reply'][index]['actionType'],
            'actions': response['ai_reply'][index]['actions']
        }
    elif 'LIST GOAL' in greeting_answer:
        ans = "Here are you selected goals.\n"
        index = 1
        user_profile = await sync_to_async(lambda: UserProfile.objects.filter(user=chat.user_id).first())()
        goals = await sync_to_async(list)(
            SelectedGoal.objects.filter(user=user_profile).values_list('goal__title', 'created_on', 'streak')
        )
        for goal in goals:
            ans += f"{index}. {goal[0]} started on {goal[1].strftime('%d %b')} with streak of {goal[2]}.\n"
        res = {
            'answer': ans,
            'character': 'Myla',
            'action_type': 'None',
            'actions': []
        }
    elif 'USER PROFILE' in greeting_answer:
        ans = random.choice(AFFIRMATIVE_RESPONSES) + "\n"
        ans += "Here's your Profile:\n"
        index = 1
        user_profile = await sync_to_async(lambda: UserProfile.objects.filter(user=chat.user_id).values_list(
            'full_name',
            'points',
            'gender'
            ).first())()
        if user_profile:
            ans += f"Name: {user_profile[0]}\n"
            ans += f"Points: {user_profile[1]}\n"
            ans += f"Gender: {user_profile[2]}\n"
            ans += "If you need more detailed information about your points or other specifics, I'll be happy to assist you.\n"
        else:
            ans += "No profile information available.\n"
        res = {
            'answer': ans,
            'character': 'Myla',
            'action_type': 'None',
            'actions': []
        }
    elif 'POINT INFO' in greeting_answer:
        ans = random.choice(AFFIRMATIVE_RESPONSES) + "\n"
        user_profile = await sync_to_async(lambda: UserProfile.objects.filter(user=chat.user_id).values_list(
            'full_name',
            'points',
            ).first())()
        
        if user_profile:
            ans += f"{user_profile[0]}, Your current points are {user_profile[1]}\n"
            
            # Fetch detailed points information
            points_details = await sync_to_async(list)(
                Points.objects.values_list('action', 'points')
            )
            
            if points_details:
                ans += "\nHere is how you can earn points:\n"
                for action, points in points_details:
                    # Map action to its human-readable label using ActionType
                    action_label = dict(ActionType.choices).get(action, action)
                    ans += f"- {action_label}: {points} points\n"
            else:
                ans += "\nNo detailed points information available.\n"
            
            # Encourage the user based on current points
            if user_profile[1] > 1000:
                ans += "\nGreat job! You're doing amazing. Keep up the good work!\n"
            elif user_profile[1] > 500:
                ans += "\nYou're making good progress! Stay consistent and you'll achieve even more.\n"
            else:
                ans += "\nEvery step counts! Keep going and you'll see great results soon.\n"
        else:
            ans += "No profile information available.\n"
        
        res = {
            'answer': ans,
            'character': 'Myla',
            'action_type': 'None',
            'actions': []
        }
    elif key == 'greeting_result' and 'QUESTION' not in greeting_answer:
        res = {
            'answer': greeting_answer,
            'character': 'Myla',
            'action_type': 'None',
            'actions': []
        }
    elif 'QUESTION' in greeting_answer:
        key, answer = await character_task
        res = {
            'answer': greeting_answer,
            'character': answer,
            'action_type': 'None',
            'actions': []
        }

    data = {"user": str(user), "character": res['character']}
    # yield data
    await asyncio.sleep(0.1)
    
    if type(conversation_history).__name__ == 'list'  and len(conversation_history) > 0:
        previous_character = conversation_history[-1]['character']
    else:
        previous_character = 'Myla'

    end_time = None

    if 'QUESTION' in res['answer']:
        key, answer = await vector_db_task

        if data['character'] != previous_character:
            message = f"Referring to {res['character']} to answer this question."
            response = await save_message_to_db(
                                        user=request.user,
                                        assistant_message=message,
                                        character = res['character'],
                                    )
            chunk = {
                    "chunk": response,
                    "last_message": True
                    }
            yield chunk #TODO add dynamic character handoff
            await asyncio.sleep(0.1)
            end_time = datetime.now()

        subscription_limitation = None
        user_subscription_plan_type = user.subscription_tier or 'FREE'
        if len(PLAN_FEATURES[user_subscription_plan_type]) >= 2:
            subscription_limitation = PLAN_FEATURES[user_subscription_plan_type][1]["description"]
        
        rag_messages = PromptHub.rag_data_answer_generator(
            user_query=query,
            character=res['character'],
            rag_similar_text=answer,
            conversation_history = conversation_history,
            previous_character = previous_character,
            subscription_limitation = subscription_limitation
        )
        answer = ""
        async for chunk in LLM.ask_from_gpt_streamed(messages = rag_messages):
            if end_time == None:
                end_time = datetime.now()
            answer += chunk
            yield {"chunk": chunk, "last_message": False}
    else:
        answer = ""
        for i in res['answer'].split(' '):
            if end_time == None:
                end_time = datetime.now()
            answer += i + ' '
            yield {"chunk": i + ' ', "last_message": False}
            await asyncio.sleep(0.1)

    # Update chat history
    chat.attrs['history'].append({
        'user_query': query,
        'llm_reply': answer,
        'character': res['character']
    })
    await sync_to_async(chat.save)(update_fields=['attrs'])
    final_response = await save_message_to_db(
        user=request.user,
        assistant_message=answer,
        character=res['character'],
        action_type=res['action_type'],
        actions=res['actions']
    )
    
    # Structure the response as a dictionary
    last_chunk = {
        "chunk": final_response,
        "last_message": True
    }
    
    # Yield the dictionary directly (let the framework handle JSON serialization)
    yield last_chunk

def get_similar_data(vector_db, namespace, request, key, query):
    print(key, datetime.now())

    embeddings = Embedder.embedding_func()
    return [key, Retriever.get_similar_doc(
        db=vector_db,
        namespace=namespace,
        user_query=query,
        embeddings=embeddings,
        similarity_score=0.6
    )]
