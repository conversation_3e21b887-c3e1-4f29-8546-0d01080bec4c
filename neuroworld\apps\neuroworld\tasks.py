import asyncio
from calendar import monthrange
from collections import defaultdict
from datetime import <PERSON><PERSON><PERSON>
from logging import getLogger
from celery import shared_task
from django.utils import timezone
from django.conf import settings
import pytz
import random

from .notifications.utils import (
    batch_notifications,
    get_all_sqs_queues,
    get_distinct_timezones,
    get_jittered_target_time,
    get_notification_template,
    get_tz_coordinated_time,
    get_user_devices,
    get_users_for_incomplete_profiles,
    get_users_for_streak_reminders,
    process_single_queue,
    format_evening_notif_for_user,
    update_last_daily_nps,
    update_last_month_nps
)

from .models import (
    NotificationSubType,
    NotificationType,
    SelectedGoal,
    UserNotification,
    UserProfile,
    Notification,
    UserDevice
)

logger = getLogger(__name__)

# Constants
STALE_TOKEN_EXPIRATION_DAYS = settings.STALE_FCM_TOKEN_EXPIRATION_DAYS
NOTIFICATION_WINDOWS = settings.NOTIFICATION_SCHEDULE

class NotificationScheduler:
    """Base class for handling notification scheduling logic."""
    
    def __init__(self, window_name):
        self.window_name = window_name
        self.config = NOTIFICATION_WINDOWS[window_name]
        self.notification = None
        
    def _should_skip_timezone(self, local_time):
        """Determine if notification should be skipped for given timezone."""
        if self.window_name == 'midday_support':
            _, last_day = monthrange(local_time.year, local_time.month)
            if local_time.day == last_day:
                return True

        is_monthly = self.config.get('monthly', False)
        if is_monthly:
            _, last_day = monthrange(local_time.year, local_time.month)
            if local_time.day != last_day or not (self.config['start_hour'] <= local_time.hour < self.config['end_hour']):
                return True

        if 'week_day' in self.config and local_time.weekday() != self.config['week_day']:
            return True

        if not is_monthly and 'week_day' not in self.config:
            if not (self.config['start_hour'] <= local_time.hour < self.config['end_hour']):
                return True

        return False

    def _has_user_received_notification_today(self, user, notification_type):
        """Check if user has already received notification today."""
        today = timezone.now().date()
        return UserNotification.objects.filter(
            user=user,
            notification__type=notification_type,
            timestamp__date=today
        ).exists()

    def _build_user_tokens(self, devices, notification, is_monthly=False):
        """Build mapping of user IDs to their notification tokens and messages."""
        user_tokens = {}
        notif_type = notification['type']

        for device in devices:
            user = device.user
            uid = user.id

            if self._has_user_received_notification_today(user, notif_type):
                logger.info(f"Skipping user {uid} - already received {notif_type} notification today.")
                continue

            message, base_notification = self._build_notification_message(user, notification, is_monthly)

            if uid not in user_tokens:
                user_tokens[uid] = {
                    'tokens': [],
                    'message': message,
                    'notification_id': str(base_notification['id'])
                }

            user_tokens[uid]['tokens'].append(device.fcm_token)

        return user_tokens

    def _build_notification_message(self, user, notification, is_monthly):
        """Build personalized notification message for user."""
        profile = user.userprofile
        message = notification['message']
        notif_subtype = notification.get('sub_type')
        notif_type = notification['type']
        base_notification = notification

        logger.info("base_notification: %s", base_notification)
        if is_monthly:
            delta_points = (profile.points or 0) - (profile.last_month_nps or 0)
            message = message.replace('[X]', str(delta_points))
        elif notif_type == NotificationType.EVENING_WRAP_UPS:
            _, local_now = get_tz_coordinated_time(profile.time_zone)
            local_today = local_now.date()

            if notif_subtype == NotificationSubType.GREAT_JOB:
                message, notif = format_evening_notif_for_user(profile, local_today, message, base_notification)
                base_notification = notif
        
        full_name = (profile.full_name or "").strip()
        first_name = full_name.split()[0] if full_name else 'User'
        return message.replace('[First Name]', first_name), base_notification

    def _process_timezone_users(self, tz_name, notification, scheduled_time, is_monthly=False):
        """Process users in a specific timezone for notification."""
        users = UserProfile.objects.filter(time_zone=tz_name).select_related('user')
        if not users:
            logger.info(f"[{self.window_name}] No users in {tz_name}")
            return

        user_ids = [u.user.id for u in users]
        devices = get_user_devices(user_ids)
        if not devices:
            logger.info(f"[{self.window_name}] No devices found for users in {tz_name}")
            return

        user_tokens = self._build_user_tokens(devices, notification, is_monthly)

        if not user_tokens:
            logger.info(f"[{self.window_name}] No eligible users for {tz_name}")
            return

        logger.info(f"[{self.window_name}] Sending notifications to {len(user_tokens)} users in {tz_name}")
        batch_notifications(user_tokens, self.window_name, scheduled_time, timezone=tz_name)

        if is_monthly:
            update_last_month_nps(user_tokens.keys(), self.window_name, tz_name)
        elif self.window_name == 'evening_wrap_ups':
            update_last_daily_nps(user_tokens.keys(), self.window_name, tz_name)

    def schedule(self):
        """Main scheduling method to be implemented by subclasses."""
        raise NotImplementedError

class DailyNotificationScheduler(NotificationScheduler):
    """Handles daily notification scheduling."""
    
    def schedule(self):
        logger.info(f"================ Starting notification scheduling for {self.window_name} ================")
        
        self.notification = self._fetch_notification_for_window()
        if not self.notification:
            logger.warning(f"[{self.window_name}] No notifications found for type {self.config['type']}")
            return

        for tz_name in get_distinct_timezones():
            self._process_notification_for_timezone(tz_name)

    def _fetch_notification_for_window(self):
        """Fetch appropriate notification for the window."""
        select_multiple = self.config.get('select_multiple', False)
        qs = Notification.objects.filter(type=self.config['type'])

        if 'sub_type' in self.config:
            qs = qs.filter(sub_type=self.config['sub_type'])

        notifications = qs.values('id','title', 'message', 'type', 'sub_type', 'icon')

        if select_multiple:
            return random.choice(list(notifications) or [None])
        
        return notifications.first()

    def _process_notification_for_timezone(self, tz_name):
        """Process notification for a specific timezone."""
        try:
            now_utc, local_time = get_tz_coordinated_time(tz_name)
            if self._should_skip_timezone(local_time):
                logger.info(f"Local time {local_time} is outside the window for timezone {tz_name}")
                return

            logger.info(f"[{self.window_name}] Processing timezone {tz_name} at local time {local_time}")
            self._process_timezone_users(
                tz_name=tz_name,
                notification=self.notification,
                scheduled_time=now_utc,
                is_monthly=self.config.get('monthly', False)
            )

        except pytz.UnknownTimeZoneError:
            logger.warning(f"Invalid timezone: {tz_name}")
        except Exception as e:
            logger.error(f"[{self.window_name}] Error in tz {tz_name}: {e}", exc_info=True)

# Task decorators for daily notifications
@shared_task
def schedule_morning():
    DailyNotificationScheduler('morning_engagements').schedule()

@shared_task
def schedule_midday():
    DailyNotificationScheduler('midday_support').schedule()

@shared_task
def schedule_evening():
    DailyNotificationScheduler('evening_wrap_ups').schedule()

# Task decorators for weekly notifications
@shared_task
def schedule_weekly_monday_engagements():
    DailyNotificationScheduler('weekly_monday_engagements').schedule()

@shared_task
def schedule_weekly_wednesday_checkin():
    DailyNotificationScheduler('weekly_wednesday_checkin').schedule()

@shared_task
def schedule_weekly_sunday_rest():
    DailyNotificationScheduler('weekly_sunday_rest').schedule()

# Task decorator for monthly engagement
@shared_task
def schedule_monthly_engagement():
    DailyNotificationScheduler('monthly_engagement').schedule()

class CheckinReminderScheduler(NotificationScheduler):
    """Handles check-in reminder notifications."""
    
    def schedule(self):
        logger.info(f"[{self.window_name}] Starting notification scheduling")
        
        try:
            self.notification = get_notification_template(
                NotificationType.CHECK_IN_REMINDER,
                NotificationSubType.CHECK_IN_REMINDER
            )

            if not self.notification:
                logger.warning(f"[{self.window_name}] No check-in reminder notifications found.")
                return

            for tz_name in get_distinct_timezones():
                self._process_checkin_timezone(tz_name)

        except Exception as e:
            logger.error(f"[{self.window_name}] Error: {str(e)}", exc_info=True)
            raise

    def _process_checkin_timezone(self, tz_name):
        """Process check-in reminders for a specific timezone."""
        try:
            now_utc, local_time = get_tz_coordinated_time(tz_name)
            start_hour, end_hour = self.config['start_hour'], self.config['end_hour']
            
            if not (start_hour <= local_time.hour < end_hour):
                logger.info(f"[{self.window_name}] Local time {local_time} is outside the window {start_hour} - {end_hour} for timezone {tz_name}.")
                return

            logger.info(f"[{self.window_name}] Processing timezone: {tz_name} at local time: {local_time}")
            today_local_date = local_time.date()

            user_profiles = UserProfile.objects.filter(time_zone=tz_name).select_related('user')
            if not user_profiles.exists():
                return

            user_ids = [profile.user.id for profile in user_profiles]
            goals = (
                SelectedGoal.objects
                .filter(user__id__in=user_ids)
                .values('user_id', 'last_tracking_date')
            )

            user_goal_map = defaultdict(list)
            for goal in goals:
                user_goal_map[goal['user_id']].append(goal['last_tracking_date'])

            eligible_user_ids = [
                user_id for user_id in user_ids
                if not any(date == today_local_date for date in user_goal_map.get(user_id, []))
            ]

            if not eligible_user_ids:
                logger.info(f"[{self.window_name}] No eligible users for check-in reminders in {tz_name}")
                return

            devices = get_user_devices(eligible_user_ids)
            if not devices.exists():
                logger.info(f"[{self.window_name}] No devices found for eligible users in {tz_name}")
                return

            user_tokens = {}
            notif_type = self.notification['type']
            for device in devices:
                uid = device.user.id

                if self._has_user_received_notification_today(device.user, notif_type):
                    logger.info(f"Skipping user {uid} - already received {notif_type} notification today.")
                    continue

                if uid not in user_tokens:
                    user_tokens[uid] = {
                        'tokens': [],
                        'message': self.notification["message"],
                        'notification_id': self.notification['id']
                    }

                user_tokens[uid]['tokens'].append(device.fcm_token)

            batch_notifications(
                user_tokens,
                self.window_name,
                scheduled_time=now_utc,
                timezone=tz_name,
            )

            logger.info(f"[{self.window_name}] Sent notifications to {len(user_tokens)} users in {tz_name}")

        except pytz.UnknownTimeZoneError:
            logger.warning(f"[{self.window_name}] Invalid timezone: {tz_name}")

@shared_task
def schedule_checkin_reminders():
    """Send daily check-in reminders between 6–8 AM local time if no goal is tracked today."""
    CheckinReminderScheduler('checkin_reminders').schedule()

class ProfileCompletionReminderScheduler(NotificationScheduler):
    """Handles profile completion reminder notifications."""
    
    def schedule(self):
        logger.info(f"Starting {self.window_name} notification scheduling")
        
        try:
            self.notification = get_notification_template(
                NotificationType.PROFILE_COMPLETION,
                NotificationSubType.COMPLETE_PROFILE
            )

            if not self.notification:
                logger.warning(f"[{self.window_name}] Notification template for profile completion not found.")
                return

            tz_name = 'America/Los_Angeles'
            now_utc, now_pst = get_tz_coordinated_time(tz_name)
            target_time = get_jittered_target_time(now_pst, hour=self.config['start_hour'])

            if abs((now_pst - target_time).total_seconds()) / 60 > 5:
                logger.info(f"[{self.window_name}] Current PST time {now_pst} is outside jitter window (target: {target_time}). Skipping.")
                return

            cutoff_date = now_utc - timedelta(days=3)
            users = get_users_for_incomplete_profiles(cutoff_date)

            if not users.exists():
                logger.info(f"[{self.window_name}] No users matched profile reminder criteria.")
                return

            user_ids = [u.user.id for u in users]
            devices = get_user_devices(user_ids)

            if not devices.exists():
                logger.info(f"[{self.window_name}] No devices found for users needing reminders.")
                return

            user_tokens = {}
            notif_type = self.notification["type"]
            for device in devices:
                uid = device.user.id

                if self._has_user_received_notification_today(device.user, notif_type):
                    logger.info(f"Skipping user {uid} - already received {notif_type} notification today.")
                    continue
                
                if uid not in user_tokens:
                    user_tokens[uid] = {
                        'tokens': [],
                        'message': self.notification["message"],
                        'notification_id': self.notification['id']
                    }

                user_tokens[uid]['tokens'].append(device.fcm_token)

            batch_notifications(user_tokens, self.window_name, now_utc, timezone=tz_name)
            logger.info(f"[{self.window_name}] Sent notifications to users with incomplete profiles.")

            for user in users:
                user.profile_completion_reminder_date = now_utc
            UserProfile.objects.bulk_update(users, ['profile_completion_reminder_date'])
            logger.info(f"[{self.window_name}] Updated profile completion reminder dates for users.")

        except Exception as e:
            logger.error(f"[{self.window_name}] Error: {str(e)}", exc_info=True)
            raise

@shared_task
def schedule_profile_completion_reminder():
    """Celery task: Sends profile completion reminder notifications around jittered 9 AM PST."""
    ProfileCompletionReminderScheduler('profile_completion_reminders').schedule()

class StreakReminderScheduler(NotificationScheduler):
    """Handles streak reminder notifications."""
    
    def schedule(self):
        logger.info(f"[{self.window_name}] Starting notification scheduling")
        
        try:
            self.notification = get_notification_template(
                NotificationType.MISSED_STREAK_REMINDER,
                NotificationSubType.STREAK_BROKEN
            )

            if not self.notification:
                logger.warning(f"[{self.window_name}] Notification template for streak reminder not found.")
                return

            tz_name = 'America/Los_Angeles'
            now_utc, now_pst = get_tz_coordinated_time(tz_name)
            target_time = get_jittered_target_time(now_pst, hour=self.config['start_hour'], minute=self.config['minute'])

            if abs((now_pst - target_time).total_seconds()) / 60 > 5:
                logger.info(f"[{self.window_name}] Current PST time {now_pst} is outside jitter window (target: {target_time}). Skipping.")
                return

            filtered_goals = get_users_for_streak_reminders(now_utc, self.window_name)
            
            if not filtered_goals:
                logger.info(f"[{self.window_name}] No goals matched streak reminder timing criteria.")
                return

            user_ids = list(set(goal.user.user.id for goal in filtered_goals))
            devices = get_user_devices(user_ids)

            if not devices.exists():
                logger.info(f"[{self.window_name}] No devices found for matched users.")
                return
        
            user_tokens = {}
            notif_type = self.notification["type"]
            for device in devices:
                uid = device.user.id

                if self._has_user_received_notification_today(device.user, notif_type):
                    logger.info(f"Skipping user {uid} - already received {notif_type} notification today.")
                    continue
        
                if uid not in user_tokens:
                    user_tokens[uid] = {
                        'tokens': [],
                        'message': self.notification["message"],
                        'notification_id': self.notification['id']
                    }

                user_tokens[uid]['tokens'].append(device.fcm_token)

            batch_notifications(user_tokens, self.window_name, now_utc, timezone=tz_name)
            profile_ids = [goal.user.id for goal in filtered_goals]
            UserProfile.objects.filter(id__in=profile_ids).update(streak_reminder_date=now_utc)
            logger.info(f"[{self.window_name}] Updated streak reminder dates for users.")

        except Exception as e:
            logger.error(f"[{self.window_name}] Error: {str(e)}", exc_info=True)
            raise

@shared_task
def schedule_streak_reminders():
    """Celery task: Sends streak reminder notifications to users based on selected goal conditions."""
    StreakReminderScheduler('missed_streak_reminders').schedule()

class WeeklyProgressScheduler(NotificationScheduler):
    """Handles weekly progress notifications."""
    
    def schedule(self):
        logger.info(f"[{self.window_name}] Starting notification scheduling")
        
        try:
            self.notification = get_notification_template(
                NotificationType.PROGRESS_SUMMARY,
                NotificationSubType.WEEKLY_RECAP
            )

            if not self.notification:
                logger.warning(f"[{self.window_name}] Notification template for weekly progress not found.")
                return

            for tz_name in get_distinct_timezones():
                self._process_weekly_timezone(tz_name)

        except Exception as e:
            logger.error(f"[{self.window_name}] Error: {str(e)}", exc_info=True)
            raise

    def _process_weekly_timezone(self, tz_name):
        """Process weekly progress notifications for a specific timezone."""
        try:
            now_utc, local_time = get_tz_coordinated_time(tz_name)

            if local_time.weekday() != self.config['week_day']:
                logger.info(f"[{self.window_name}] Weekday is not Sunday for timezone {tz_name}. Skipping.")
                return

            target_time = get_jittered_target_time(local_time, hour=self.config['start_hour'])

            if abs((local_time - target_time).total_seconds()) / 60 > 5:
                logger.info(f"[{self.window_name}] Current PST time {local_time} is outside jitter window (target: {target_time}). Skipping.")
                return

            logger.info(f"[{self.window_name}] Processing timezone: {tz_name} at local time: {local_time}")

            user_profiles = UserProfile.objects.filter(time_zone=tz_name).select_related('user')
            if not user_profiles.exists():
                logger.info(f"[{self.window_name}] No users in {tz_name}")
                return

            user_ids = [profile.user.id for profile in user_profiles]
            devices = get_user_devices(user_ids)

            if not devices.exists():
                logger.info(f"[{self.window_name}] No devices found for users in {tz_name}")
                return

            user_tokens = {}
            notif_type = self.notification["type"]
            for device in devices:
                uid = device.user.id

                if self._has_user_received_notification_today(device.user, notif_type):
                    logger.info(f"Skipping user {uid} - already received {notif_type} notification today.")
                    continue
        
                if uid not in user_tokens:
                    user_tokens[uid] = {
                        'tokens': [],
                        'message': self.notification["message"].replace('[X]', str((profile.points or 0) - (profile.last_week_nps or 0))),
                        'notification_id': self.notification['id']
                    }
                user_tokens[uid]['tokens'].append(device.fcm_token)

            batch_notifications(
                user_tokens,
                self.window_name,
                scheduled_time=now_utc,
                timezone=tz_name,
            )

            logger.info(f"[{self.window_name}] Sent notifications to {len(user_tokens)} users in {tz_name}")

            for profile in user_profiles:
                profile.last_week_nps = profile.points
            UserProfile.objects.bulk_update(user_profiles, ['last_week_nps'])

            logger.info(f"[{self.window_name}] Updated last_week_nps for users in {tz_name}")

        except pytz.UnknownTimeZoneError:
            logger.warning(f"[{self.window_name}] Invalid timezone: {tz_name}")

@shared_task
def schedule_weekly_progress_notifications():
    """Send weekly progress notifications every Sunday at 7 PM local time ± 20 minutes."""
    WeeklyProgressScheduler('weekly_progress_summary').schedule()

class ReEngagementScheduler(NotificationScheduler):
    """Handles re-engagement notifications for inactive users."""
    
    def schedule(self):
        logger.info(f"[{self.window_name}] Starting notification scheduling")
        
        try:
            # Get all notifications of type RE_ENGAGEMENT
            notifications = Notification.objects.filter(
                type=NotificationType.RE_ENGAGEMENT
            ).values('title', 'message', 'type', 'sub_type', 'icon')
            
            if not notifications.exists():
                logger.warning(f"[{self.window_name}] No re-engagement notifications found.")
                return
                
            # Randomly select one notification
            self.notification = random.choice(list(notifications))
            
            for tz_name in get_distinct_timezones():
                self._process_reengagement_timezone(tz_name)

        except Exception as e:
            logger.error(f"[{self.window_name}] Error: {str(e)}", exc_info=True)
            raise

    def _process_reengagement_timezone(self, tz_name):
        """Process re-engagement notifications for a specific timezone."""
        try:
            now_utc, local_time = get_tz_coordinated_time(tz_name)
            start_hour, end_hour = self.config['start_hour'], self.config['end_hour']
            # Check if current time is between 3-5 PM
            if not (start_hour <= local_time.hour < end_hour):
                logger.info(f"[{self.window_name}] Current time {local_time} is outside 3-5 PM window for timezone {tz_name}")
                return

            logger.info(f"[{self.window_name}] Processing timezone: {tz_name} at local time: {local_time}")

            # Get cutoff date for 4 days ago
            cutoff_date = now_utc - timedelta(days=4)

            # Get all users in this timezone
            user_profiles = UserProfile.objects.filter(time_zone=tz_name).select_related('user')
            if not user_profiles.exists():
                logger.info(f"[{self.window_name}] No users found in timezone {tz_name}")
                return

            # Get all devices for these users
            user_ids = [profile.user.id for profile in user_profiles]
            all_devices = UserDevice.objects.filter(user_id__in=user_ids).select_related('user', 'user__userprofile')

            # Group devices by user
            user_devices = defaultdict(list)
            for device in all_devices:
                user_devices[device.user.id].append(device)

            # Build user tokens mapping
            user_tokens = {}
            notif_type = self.notification["type"]
            profiles_to_update = []
            
            for user_id, devices in user_devices.items():
                user = devices[0].user  # All devices belong to same user
                profile = user.userprofile
                
                # Skip if user has received notification in last 4 days
                if profile.inactivity_reminder_date and (now_utc - profile.inactivity_reminder_date).days < 4:
                    logger.info(f"Skipping user {user_id} - last inactivity reminder was less than 4 days ago.")
                    continue
                
                if self._has_user_received_notification_today(user, notif_type):
                    logger.info(f"Skipping user {user_id} - already received {notif_type} notification today.")
                    continue

                # Check if ALL devices for this user are inactive (updated_at <= cutoff_date)
                all_devices_inactive = all(device.updated_at <= cutoff_date for device in devices)
                if not all_devices_inactive:
                    logger.info(f"Skipping user {user_id} - has active devices")
                    continue

                user_tokens[user_id] = {
                    'tokens': [device.fcm_token for device in devices],
                    'message': self.notification["message"],
                    'notification_id': self.notification['id']
                }
                profiles_to_update.append(profile)

            if not user_tokens:
                logger.info(f"[{self.window_name}] No eligible users for notifications in {tz_name}")
                return

            # Send notifications
            batch_notifications(
                user_tokens,
                self.window_name,
                scheduled_time=now_utc,
                timezone=tz_name,
            )

            # Update inactivity_reminder_date for users who received notifications
            for profile in profiles_to_update:
                profile.inactivity_reminder_date = now_utc
            UserProfile.objects.bulk_update(profiles_to_update, ['inactivity_reminder_date'])

            logger.info(f"[{self.window_name}] Updated inactivity reminder dates for {len(profiles_to_update)} users")

        except pytz.UnknownTimeZoneError:
            logger.warning(f"[{self.window_name}] Invalid timezone: {tz_name}")

@shared_task
def schedule_re_engagement_reminders():
    """Send re-engagement notifications to inactive users between 3-5 PM local time."""
    ReEngagementScheduler('re_engagement_reminders').schedule()

@shared_task
def process_sqs_queues():
    """Process all SQS queues asynchronously."""
    logger.info("Starting SQS queue consumer.")
    queue_names = get_all_sqs_queues()

    async def run_all():
        tasks = [process_single_queue(q_name, window_name) for window_name, q_name in queue_names]
        await asyncio.gather(*tasks)

    asyncio.run(run_all())
    logger.info("Finished processing all SQS queues.")

@shared_task
def delete_stale_fcm_tokens():
    """Delete FCM tokens that have not been updated in the last STALE_FCM_TOKEN_EXPIRATION_DAYS days."""
    logger.info("Starting deletion of stale FCM tokens.")
    cutoff_date = timezone.now() - timedelta(days=STALE_TOKEN_EXPIRATION_DAYS)
    stale_tokens = UserDevice.objects.filter(updated_at__lt=cutoff_date)

    if not stale_tokens.exists():
        logger.info("No stale FCM tokens found.")
        return

    stale_tokens.delete()
    logger.info(f"Deleted {stale_tokens.count()} stale FCM tokens.")
