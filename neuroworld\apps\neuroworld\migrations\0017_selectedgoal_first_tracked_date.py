# Generated by Django 4.2.18 on 2025-04-25 17:21

from django.db import migrations, models

from ..models import SelectedGoal

def shift_start_date(apps, schema_editor):
    # For one time migration, to maintain data integrity for existing records
    # Update only those rows where first_tracked_date is null and start_date is not null
    for goal in SelectedGoal.objects.filter(first_tracked_date__isnull=True, start_date__isnull=False):
        goal.first_tracked_date = goal.start_date  # Move start_date value to first_tracked_date
        goal.save()

class Migration(migrations.Migration):

    dependencies = [
        ('neuroworld', '0016_userprofile_time_zone'),
    ]

    operations = [
        migrations.AddField(
            model_name='selectedgoal',
            name='first_tracked_date',
            field=models.DateField(default=None, null=True),
        ),
        migrations.RunPython(shift_start_date),
    ]
