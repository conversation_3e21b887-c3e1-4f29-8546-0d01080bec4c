# Generated by Django 4.2.18 on 2025-06-19 19:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('neuroworld', '0034_chat'),
    ]

    operations = [
        migrations.AddField(
            model_name='badge',
            name='badge_type',
            field=models.CharField(blank=True, choices=[('streak', 'Streak'), ('city', 'City')], max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='badge',
            name='category',
            field=models.CharField(blank=True, choices=[('nutrition', 'Nutrition'), ('exercise', 'Exercise'), ('unwind', 'Unwind'), ('restore', 'Restore'), ('optimize', 'Optimize')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='badge',
            name='city_area',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='badge',
            name='level',
            field=models.CharField(blank=True, choices=[('bronze', 'Bronze'), ('silver', 'Silver'), ('gold', 'Gold'), ('sapphire', 'Sapphire'), ('diamond', 'Diamond'), ('platinum', 'Platinum')], max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='badge',
            name='required_habits',
            field=models.IntegerField(default=1),
        ),
        migrations.AddField(
            model_name='badge',
            name='required_plots',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='badge',
            name='required_streak',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userbadge',
            name='earned_at',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AlterUniqueTogether(
            name='badge',
            unique_together={('badge_type', 'level', 'city_area'), ('badge_type', 'level', 'category')},
        ),
        migrations.AlterUniqueTogether(
            name='userbadge',
            unique_together={('user', 'badge')},
        ),
    ]
