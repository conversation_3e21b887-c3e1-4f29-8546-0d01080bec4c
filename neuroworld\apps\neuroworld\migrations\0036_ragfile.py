# Generated by Django 4.2.18 on 2025-06-19 07:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('neuroworld', '0035_badge_badge_type_badge_category_badge_city_area_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='RAGFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.CharField(max_length=255, verbose_name='File Name')),
                ('weaviate_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Weaviate ID')),
                ('s3_url', models.URLField(blank=True, null=True, verbose_name='S3 URL')),
                ('file', models.FileField(blank=True, null=True, upload_to='rag_files/', verbose_name='Upload File')),
            ],
        ),
    ]
