from rest_framework.reverse import reverse
from rest_framework.test import APITestCase
from http import HTTPStatus


# class AuthorAPIViewTest(APITestCase):
#     """This class tests the author CRUD api"""

#     @classmethod
#     def setUpTestData(cls):
#         """creating 4 authors"""
#         number_of_authors = 2
#         for author in range(number_of_authors):
#             author = Author.objects.create(
#                 first_name=f"firstname {author}",
#                 last_name=f"lastname {author}",
#             )
        

#     def test_read_author_list_response(self):
#         """Checking to see if th authors api exists at the desired location"""
#         # Note: according to the django REST framework docs, it is considered best practice to use the 
#         # absolute url of web apis. Also note that if we were to use the url 
#         # 'http://127.0.0.1:8000/catalog/api/authors' without the forward slash,it would 
#         # return status code 301 (moved permanently) and then redirect to the address with the forward slash
#         url = reverse("author-api-list")
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, HTTPStatus.OK)

#     def test_read_author_api_list_content(self):
#         """
#         This tests to see if the body of the response is what we expect
#         i.e a json that contains a list of authors with the correct names and ids
#         """
#         url = reverse("author-api-list")
#         response = self.client.get(url, format="json")
#         response_body = response.json()
#         expected_response = [
#             {
#                 "id": 1,
#                 "first_name": "firstname 0",
#                 "last_name": "lastname 0",
#                 "date_of_birth": None,
#                 "date_of_death": None,
#             },
#             {
#                 "id": 2,
#                 "first_name": "firstname 1",
#                 "last_name": "lastname 1",
#                 "date_of_birth": None,
#                 "date_of_death": None,
#             },
#         ]

#         self.assertEqual(response_body, expected_response)

#     def test_read_author_id_api_response(self):
#         """Tests to see if the get request to author id 1 returns 200 or not"""
#         url = reverse("author-api-detail", args=[1])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, HTTPStatus.OK)

#     def test_read_author_id_body(self):
#         """Tests the body of the get request to the author with id = 1"""
#         url = reverse("author-api-detail", args=[1])
#         response = self.client.get(url, format="json")
#         response_body = response.json()
#         expected_response = {
#             "id": 1,
#             "first_name": "firstname 0",
#             "last_name": "lastname 0",
#             "date_of_birth": None,
#             "date_of_death": None,
#         }

#         self.assertEqual(response_body, expected_response)

#     def test_create_author_response(self):
#         """This tests whether or not an author is created"""
#         data = {"first_name": "John", "last_name": "Doe"}
#         url = reverse("author-api-list")
#         response = self.client.post(url, data, format="json")

#         # Checking status code 201 created
#         self.assertEqual(response.status_code, HTTPStatus.CREATED)


#     def test_create_author_body(self):
#         """This tests the body of the response when author is created"""
#         data = {"first_name": "John", "last_name": "Doe"}
#         url = reverse("author-api-list")
#         response = self.client.post(url, data, format="json")

#         response_body = response.json()
#         expected_response = {
#             "id": 3,
#             "first_name": "John",
#             "last_name": "Doe",
#             "date_of_birth": None,
#             "date_of_death": None,
#         }
#         # Checking whether the response was as expected
#         self.assertEqual(response_body, expected_response)


#     def test_update_author_response_with_authorization(self):
#         """This tests the update author response"""
#         data = {"first_name": "John", "last_name": "Doe"}
#         url = reverse("author-api-detail", args=[1])
#         response = self.client.put(url, data, format="json")

#         # Checking status code 201 created
#         self.assertEqual(response.status_code, HTTPStatus.OK)

#     def test_update_author(self):
#         """This tests whether or not an author is updated in the database"""
        
#         data = {"first_name": "John", "last_name": "Doe"}
#         # Get the orginal value of the author to be updated
#         author_2 = Author.objects.get(id=2)

#         # Update the chosen author
#         url = reverse("author-api-detail", args=[2])
#         self.client.put(url, data, format="json")

#         # Check whether the author is updated in the database
#         self.assertNotEqual(str(author_2), str(Author.objects.get(id=2)))


#     def test_delete_author_response_with_authorization(self):
#         """This tests whether or not an author is deleted"""
#         url = reverse("author-api-detail", args=[2])
#         response = self.client.delete(url)

#         # 204 means that the request completed but no reponse was returned
#         self.assertEqual(response.status_code, HTTPStatus.NO_CONTENT)

#     def test_author_was_deleted(self):
#         """This tests whether or not an author was deleted successfuly in the database"""
#         # Count the number of authors before the delete
#         count_before_delete = Author.objects.count()
#         url = reverse("author-api-detail", args=[2])
#         self.client.delete(url)

#         # Count number of authors after delete
#         count_after_delete = Author.objects.count()
#         self.assertLess(count_after_delete, count_before_delete)
