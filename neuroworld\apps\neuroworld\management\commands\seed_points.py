from django.core.management.base import BaseCommand
from apps.neuroworld.models import Points, ActionType
import random

class Command(BaseCommand):
    help = 'Seed Points records with random values'

    def handle(self, *args, **kwargs):
        action_points = {
            ActionType.DAILY_APP_OPEN: 50,
            ActionType.COMMUNITY_ENGAGEMENT_BONUS: 25,
            ActionType.DAILY_CHECKIN: 300,
            ActionType.EXTRA_CHECKIN_BONUS: 400,
            ActionType.WEEKLY_STREAK: 500,
            ActionType.MONTHLY_STREAK: 900,
            ActionType.THREE_MONTHS_STREAK: 2000,
            ActionType.SIX_MONTHS_STREAK: 5000,
            ActionType.ONE_OFF_BIG_ACTIONS: 3000,
            ActionType.RESTORE_STREAK_COST: 1500,
        }

        for action, points_value in action_points.items():
            points_record, created = Points.objects.get_or_create(action=action, defaults={'points': points_value})
            if not created:
                points_record.points = points_value
                points_record.save()

        self.stdout.write(self.style.SUCCESS('Seeded Points records successfully.'))