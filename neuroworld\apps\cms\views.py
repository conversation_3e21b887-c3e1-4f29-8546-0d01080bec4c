
from wagtail.admin.viewsets.model import ModelViewSet
from .models import AIAgentReferenceFile, Character

class AIAgentReferenceFileViewSet(ModelViewSet):
    model = AIAgentReferenceFile
    form_fields = ["temp_file"]
    icon = "doc-full-inverse"
    add_to_admin_menu = True
    copy_view_enabled = False
    inspect_view_enabled = True
    menu_label = "AI Agent Files" # This label will appear in the Wagtail admin menu

    list_display = ["file_name", "presigned_url_link"]
    
    form_field_overrides = {
        'temp_file': {'required': True}
    }

ai_agent_files_viewset = AIAgentReferenceFileViewSet("ai_agent_reference_files")

class CharacterViewSet(ModelViewSet):
    model = Character
    form_fields = ["name", "role", "tone", "background", "few_shots_input"]
    icon = "user"
    add_to_admin_menu = True
    copy_view_enabled = False
    inspect_view_enabled = True
    list_display = ["name", "role", "tone"]

character_viewset = CharacterViewSet("character")