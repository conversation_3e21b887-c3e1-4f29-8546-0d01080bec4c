
<!-- PROJECT LOGO -->
<br />
<div align="center">
    <img src="https://picsum.photos/seed/picsum/200/300" alt="Logo" >

<h3 align="center">Neuroworld</h3>
<p align="center">
    <a href="#"><strong>Explore the docs »</strong></a>
</div>

<!-- TABLE OF CONTENTS -->
<details>
  <summary>Table of Contents</summary>
  <ol>
    <li>
      <a href="#about-the-project">About The Project</a>
      <ul>
        <li><a href="#built-with">Built With</a></li>
      </ul>
    </li>
    <li>
      <a href="#getting-started">Getting Started</a>
      <ul>
        <li><a href="#prerequisites">Prerequisites</a></li>
        <li><a href="#installation">Installation</a></li>
      </ul>
    </li>
    <li><a href="#how-to-run-tests">How to run tests</a></li>
    <li><a href="#how-to-run-lint">How to run lint</a></li>
    <li><a href="#how-to-debug-locally">How to debug locally</a></li>
    <li><a href="#license">License</a></li>
    <li><a href="#contact">Contact</a></li>
  </ol>
</details>



<!-- ABOUT THE PROJECT -->
## About The Project

[![Product Name Screen Shot][product-screenshot]](https://example.com)


Description Some project description

Some important points.
* Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled
* Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled
* Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled


### Built With

This section should list any major frameworks/libraries used to bootstrap your project.

* [![Next][Next.js]][Next-url]
* [![React][React.js]][React-url]
* [![Vue][Vue.js]][Vue-url]
* [![Angular][Angular.io]][Angular-url]
* [![Svelte][Svelte.dev]][Svelte-url]
* [![Laravel][Laravel.com]][Laravel-url]
* [![Bootstrap][Bootstrap.com]][Bootstrap-url]
* [![JQuery][JQuery.com]][JQuery-url]

<!-- GETTING STARTED -->
## Getting Started

This is an example of how you may give instructions on setting up your project locally.

### Prerequisites

Required resource access for getting setup completed:
- Setting Git Hub Access using SSH key
- [pipenv - to manage a Python virtual environment and dependencies.](https://pipenv.pypa.io/en/latest/)
- [postgres - a free and open-source relational database management system](https://www.postgresql.org/download/)
- [pre-commit - to setup pre-commit hooks for linting and code styling](https://pre-commit.com/)


### Installation

The first thing to do is to clone the repository:


1. Clone the repo
   ```sh
   git clone https://github.com/your_username_/Project-Name.git
   ```
2. cd into the new directory and setup the virtual environment with  pipenv
   ```
   cd project
   pipenv install
   ```
   This should install all the dependencies

3. Install postgress
  ```
  sudo apt update -y
  sudo apt install postgresql postgresql-contrib
   ```

4. Create a local postgres database for your development environment.

5. go to neuroworld.neuroworld.settings and add your local postgres databases url as the default url in the following line. by default this will set to ```postgres://postgres:postgres@127.0.0.1:5432/neuroworld```

```
DATABASES = {
    "default": env.db(
        "DATABASE_URL",
        default="postgres://postgres:postgres@127.0.0.1:5432/neuroworld",
    ),
}
```

6. start the virtual environment shell
```
pipenv shell
```

7. Apply migrations
  ```
  python3 manage.py migrate
   ```
8. Locally check server running command
  ```
  python3 manage.py runserver
   ```

<!-- How to run tests  -->
## How to run tests
The easiest way to run all the tests is to use the command:
  ```
  pipenv shell
  python3 manage.py test
   ```

## Commiting your changes with Git Hooks

1. Once you are ready to commit your changes, install [pre-commit](https://pre-commit.com/) and run

```
pre-commit  install
```

2. Commit your changes
```
git add .
git commit -m "my first commit"
```

You should see the git hooks check your code for formatting and styling. The hooks should automattically fix issues where possible but you might asked to manually change some things as well.

<!-- How to run lint  -->
## How to run lint 
Steps to mention to run linter locally

<!-- How to debug locally  -->
## How to debug locally
Steps to mention the setup debugger locally.


<!-- Troubleshooting  -->
## Troubleshooting 
Here you mention some common issues which most of the team facing this will help other devs to overcome issue with minimal time.

<!-- LICENSE -->
## License

Distributed under the MIT License. See `LICENSE.txt` for more information.


<!-- CONTACT -->
## Contact

Your Name - [@your_twitter](https://twitter.com/your_username) - <EMAIL>

Project Link: [https://github.com/your_username/repo_name](https://github.com/your_username/repo_name)

<!-- MARKDOWN LINKS & IMAGES -->
<!-- https://www.markdownguide.org/basic-syntax/#reference-style-links -->
[contributors-shield]: https://img.shields.io/github/contributors/othneildrew/Best-README-Template.svg?style=for-the-badge
[contributors-url]: https://github.com/othneildrew/Best-README-Template/graphs/contributors
[forks-shield]: https://img.shields.io/github/forks/othneildrew/Best-README-Template.svg?style=for-the-badge
[forks-url]: https://github.com/othneildrew/Best-README-Template/network/members
[stars-shield]: https://img.shields.io/github/stars/othneildrew/Best-README-Template.svg?style=for-the-badge
[stars-url]: https://github.com/othneildrew/Best-README-Template/stargazers
[issues-shield]: https://img.shields.io/github/issues/othneildrew/Best-README-Template.svg?style=for-the-badge
[issues-url]: https://github.com/othneildrew/Best-README-Template/issues
[license-shield]: https://img.shields.io/github/license/othneildrew/Best-README-Template.svg?style=for-the-badge
[license-url]: https://github.com/othneildrew/Best-README-Template/blob/master/LICENSE.txt
[linkedin-shield]: https://img.shields.io/badge/-LinkedIn-black.svg?style=for-the-badge&logo=linkedin&colorB=555
[linkedin-url]: https://linkedin.com/in/othneildrew
[product-screenshot]: images/screenshot.png
[Next.js]: https://img.shields.io/badge/next.js-000000?style=for-the-badge&logo=nextdotjs&logoColor=white
[Next-url]: https://nextjs.org/
[React.js]: https://img.shields.io/badge/React-20232A?style=for-the-badge&logo=react&logoColor=61DAFB
[React-url]: https://reactjs.org/
[Vue.js]: https://img.shields.io/badge/Vue.js-35495E?style=for-the-badge&logo=vuedotjs&logoColor=4FC08D
[Vue-url]: https://vuejs.org/
[Angular.io]: https://img.shields.io/badge/Angular-DD0031?style=for-the-badge&logo=angular&logoColor=white
[Angular-url]: https://angular.io/
[Svelte.dev]: https://img.shields.io/badge/Svelte-4A4A55?style=for-the-badge&logo=svelte&logoColor=FF3E00
[Svelte-url]: https://svelte.dev/
[Laravel.com]: https://img.shields.io/badge/Laravel-FF2D20?style=for-the-badge&logo=laravel&logoColor=white
[Laravel-url]: https://laravel.com
[Bootstrap.com]: https://img.shields.io/badge/Bootstrap-563D7C?style=for-the-badge&logo=bootstrap&logoColor=white
[Bootstrap-url]: https://getbootstrap.com
[JQuery.com]: https://img.shields.io/badge/jQuery-0769AD?style=for-the-badge&logo=jquery&logoColor=white
[JQuery-url]: https://jquery.com 
[product-screenshot]: https://fakeimg.pl/600/
