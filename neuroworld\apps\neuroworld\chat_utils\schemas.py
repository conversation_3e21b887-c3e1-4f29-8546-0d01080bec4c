from typing          import Annotated
from fastapi         import File, UploadFile
from pydantic        import BaseModel

class BasicRequest(BaseModel):
  pass

class User(BaseModel):
  username: str
  password: str

class DeleteTag(BasicRequest):
  uuid: str
  
class DeleteUser(BasicRequest):
  uuid: str

class ChatQnA(BaseModel):
  chat_uuid: str
  user_query: str

class UploadResumes(BasicRequest):
  files:         list[Annotated[UploadFile, File()]]

class UploadResumesViaPath(BasicRequest):
  folder_path:   str
