import os
from django.conf import settings
import openai
from datetime import datetime

class LLM:
    async def async_ask_from_gpt(messages, key='default', model_name = settings.OPENAI_MODEL_NAME):
        print(key, datetime.now())

        answer = ""
        client = openai.AsyncOpenAI(api_key = settings.OPEN_AI_KEY)
        response = await client.chat.completions.create(
            model = model_name,
            messages = messages,
            stream = True,
            temperature=0
        )
        async for chunk in response:
            content = chunk.choices[0].delta.content
            if content:
                answer += content
        print(key, "Done", datetime.now())
        return [key, answer]
    
    def ask_from_gpt(messages, model_name=settings.OPENAI_MODEL_NAME):
        client = openai.OpenAI(api_key=settings.OPEN_AI_KEY)

        response = client.chat.completions.create(
            model=model_name,
            messages=messages,
            temperature=0,
            stream=False  # Optional, defaults to False
        )

        answer = response.choices[0].message.content
        return answer

    async def ask_from_gpt_streamed(messages, key='default', model_name = settings.OPENAI_MODEL_NAME):
        print(key, datetime.now())

        client = openai.AsyncOpenAI(api_key = settings.OPEN_AI_KEY)
        response = await client.chat.completions.create(
            model = model_name,
            messages = messages,
            stream = True,
            temperature=0
        )
        async for chunk in response:
            content = chunk.choices[0].delta.content
            if content:
                yield content
