{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Account Deletion Status - Neuroworld</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #f2f3f6;
            font-family: Arial, Helvetica, sans-serif;
            -webkit-font-smoothing: antialiased;
        }
        .container {
            max-width: 600px;
            margin: 80px auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        .header {
            background-color: #1994C5;
            padding: 30px;
            text-align: center;
        }
        .header img {
            height: 41px;
        }
        .body {
            padding: 30px;
            text-align: center;
        }
        .body h2 {
            color: #F22E30;
            font-size: 18px;
            margin: 20px 0;
        }
        .body p {
            color: #4B4B4B;
            font-size: 14px;
            font-weight: 500;
            line-height: 24px;
            margin: 0;
        }
        .status {
            margin-top: 24px;
            font-size: 16px;
            color: #242A33;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{% static 'neuro-logo.svg' %}" alt="Neuroworld">
        </div>
        <div class="body">
            <h2>Account Deletion Acknowledgement</h2>
            <p id="waiting-message">Please wait while we verify your request.</p>
            <p class="status" id="status"></p>
        </div>
    </div>  

    {% csrf_token %}
    <script>
    (function () {
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (const cookie of cookies) {
                    const trimmed = cookie.trim();
                    if (trimmed.startsWith(name + '=')) {
                        cookieValue = decodeURIComponent(trimmed.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        const statusEl = document.getElementById('status');
        const waitingEl = document.getElementById('waiting-message');
        const params = new URLSearchParams(window.location.search);
        const token = params.get('token');

        if (!token) {
            waitingEl.style.display = 'none';
            statusEl.innerText = '❌ Invalid request. No token provided.';
            return;
        }

        const csrftoken = getCookie('csrftoken');

        // Show waiting message while fetching
        waitingEl.style.display = 'block';
        statusEl.innerText = '';

        fetch(`/neuroworld/api/delete-account/?token=${encodeURIComponent(token)}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrftoken
            },
            credentials: 'include'
        })
        .then(response => response.json().then(data => ({
            ok: response.ok,
            body: data
        })))
        .then(result => {
            waitingEl.style.display = 'none';  // Hide waiting message

            if (result.ok) {
                statusEl.innerText = '✅ Your account has been successfully deleted.';
            } else {
                statusEl.innerText = `❌ ${result.body.error || 'Failed to delete account.'}`;
            }
        })
        .catch(error => {
            waitingEl.style.display = 'none';  // Hide waiting message
            console.error(error);
            statusEl.innerText = '❌ An unexpected error occurred.';
        });
    })();

    </script>

</body>
</html>
