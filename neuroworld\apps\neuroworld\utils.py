import json
from datetime import datetime
import random
import string
import uuid
import ast


from django.db import IntegrityError
from django.http import JsonResponse

from .serializers import NeuroChatSerializer
import jwt
import requests
from .models import ActionType, GoalVersion, NeuroChat, Notification, NotificationSubType, NotificationType, PlanTier, Points, SelectedGoal, User, UserDevice, UserNotification, UserProfile, GoalTracking, UserSubscription
import boto3
import logging
from botocore.exceptions import NoCredentialsError, ClientError
from django.core.mail import send_mail
from django.conf import settings
from django.contrib.auth.tokens import default_token_generator 
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes
from django.shortcuts import render
from asgiref.sync import sync_to_async

log = logging.getLogger(__name__)

def convert_uuid_to_str(data):
    if isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, uuid.UUID):
                data[key] = str(value)
            elif isinstance(value, dict):
                convert_uuid_to_str(value)
            elif isinstance(value, list):
                for item in value:
                    convert_uuid_to_str(item)
    elif isinstance(data, list):
        for item in data:
            convert_uuid_to_str(item)
    return data


def send_email_via_ses(sender, recipient, subject, html_content, text_content=None, cc=None):
    """
    Sends an email using AWS SES.
    """
    client = boto3.client('ses', region_name=settings.AWS_REGION)
    destination = {'ToAddresses': recipient}
    
    if cc:
        destination['CcAddresses'] = cc
    
    body = {'Html': {'Data': html_content}}
    if text_content:
        body['Text'] = {'Data': text_content}
    
    try:
        client.send_email(
            Source=sender,
            Destination=destination,
            Message={
                'Subject': {'Data': subject},
                'Body': body
            }
        )
    except (NoCredentialsError, ClientError) as e:
        logging.error(f"SES email sending error: {e}")
        raise

def send_email_via_smtp(sender, recipient, subject, html_content, text_content=None, cc=None):
    """
    Sends an email using Django SMTP.
    """
    recipient_list = recipient + (cc if cc else [])
    message = text_content if text_content else html_content
    
    try:
        send_mail(
            subject,
            message,
            sender,
            recipient_list,
            html_message=html_content
        )
    except Exception as e:
        logging.error(f"SMTP email sending error: {e}")
        raise

def send_email(
    sender: str, 
    recipient: list, 
    subject: str, 
    html_content: str, 
    text_content: str = None, 
    cc: list = None
):
    """
    Sends an email using AWS SES or SMTP based on settings.
    """
    try:
        if settings.USE_AWS_SES:
            send_email_via_ses(sender, recipient, subject, html_content, text_content, cc)
        else:
            send_email_via_smtp(sender, recipient, subject, html_content, text_content, cc)
    except Exception as e:
        logging.error(f"Failed to send email: {e}")
        raise
    
def generate_token(user):
    token = default_token_generator.make_token(user)
    user_id = urlsafe_base64_encode(force_bytes(user.pk))
    characters = string.ascii_letters + string.digits
    random_string = ''.join(random.choices(characters, k=6))
    return f"{random_string}{user_id}-{token}"


def user_has_goal_in_category(user_profile : UserProfile, category: str):
    has_goal = SelectedGoal.objects.filter(
        user=user_profile,
        goal_version__goal__category=category,
        is_active=True
    ).exists()
    
    if user_profile.user.subscription_tier == PlanTier.FREE and has_goal:
        return True
    else: 
        return False
    

def serve_assetlinks(request):
    """
    Dynamically generate the assetlinks.json response based on the environment.
    """
    environment = settings.ENVIRONMENT_NAME  # Ensure this is defined in your settings
    package_name = f"com.teamsharzai.neuroworld.{environment}"

    sha256_cert_fingerprints = {
        "dev": "55:2A:5A:49:AF:FA:CE:C6:1C:9A:B6:DD:90:2A:6C:88:AB:4A:80:86:02:0E:55:6D:0D:6E:0B:E4:C1:76:C9:A5",
        "qa": "C1:DB:F0:94:C9:7C:76:C5:28:54:CF:1E:A0:7D:CB:C1:F0:FD:71:6F:57:71:8A:A5:F4:9A:D6:58:41:9C:8D:DC",
        "staging": "0C:F1:98:EE:80:C6:8E:0A:4B:30:D7:8B:C7:92:E0:7B:71:20:9B:DB:62:CB:DA:87:4B:BC:2D:03:FD:4A:0A:16",
        "prod": "7A:02:77:F5:FD:89:B1:F0:D9:6A:8C:F8:A9:2D:73:02:D5:94:28:C6:17:ED:D1:67:DE:B2:8A:28:EF:78:E9:B9",
    }

    fingerprint = sha256_cert_fingerprints.get(environment)
    if not fingerprint:
        return JsonResponse({"error": "Invalid environment configuration"}, status=500)

    return JsonResponse([{
        "relation": ["delegate_permission/common.handle_all_urls"],
        "target": {
            "namespace": "android_app",
            "package_name": package_name,
            "sha256_cert_fingerprints": [fingerprint]
        }
    }], safe=False)

def serve_apple_site_association(request):
    environment = settings.ENVIRONMENT_NAME  # Ensure this is defined in your settings
    app_id = f"25PAYB68R2.com.teamsharzai.neuroworld.{environment}"

    content = {
        "applinks": {
            "apps": [],
            "details": [
                {
                    "appIDs": [app_id],
                    "components": [
                        { "/": "/reset-password/*" }
                    ]
                }
            ]
        }
    }
    return JsonResponse(content, content_type="application/json")

# Goal Tracking Utils

def track_goal(selected_goal, tracking_date, is_new_week, should_reset_goal ):
    """
    Track the goal for the given date
    """
    user = selected_goal.user
    if not user.first_tracked_date: 

        user.first_tracked_date = tracking_date
        user.save()

    # To indicate stale data and trigger refetch on frontend
    if selected_goal.last_tracking_date and selected_goal.last_tracking_date >= tracking_date:
        raise PermissionError("Unable to check-in to the selected goal. Please try again!")
    
    #TODO: Add proper goal version handling when in place
    #Query Goalversion for the given goal and and level = 'BEGINNER'
    goal_version = GoalVersion.objects.get(goal=selected_goal.goal, level="Beginner")

    GoalTracking.objects.create(selected_goal=selected_goal, goal_version=goal_version, date=tracking_date)
    selected_goal.last_tracking_date = tracking_date

    awarded_points = {}
    # Start a fresh week on any of the following case: 
    # Case 1: First checkin after goal creation
    if not selected_goal.start_date :
        selected_goal.first_tracked_date = tracking_date
        selected_goal.start_date = tracking_date
        selected_goal.current_checkins = 1
        selected_goal.current_week = 1

    #case 2: reset goal
    elif should_reset_goal:
        selected_goal.start_date = tracking_date
        selected_goal.current_week = 1
        selected_goal.streak_awarded = False
        selected_goal.streak = 0
        selected_goal.current_checkins = 1
        selected_goal.restore_week_offset = 0

    # case 3: same week goal track
    elif not is_new_week:
        selected_goal.current_checkins += 1
    
    #case 4: new week goal track
    elif not should_reset_goal:
        selected_goal.current_week += 1
        selected_goal.current_checkins = 1
        selected_goal.streak_awarded = False


    awarded_points = award_points(selected_goal)

    selected_goal.save()

    return {
        "awarded_points": awarded_points, 
        "assets": [], 
        "badges": [], 
    }


def award_points(selected_goal, is_edit_goal=False):
    """
    Handles points for the given goal
    """
    awarded_points = {}

    ### Award daily check-in points ###
    current_points = selected_goal.user.points

    if not is_edit_goal:
        if selected_goal.current_checkins > selected_goal.target or selected_goal.streak_awarded:
            awarded_points[ActionType.EXTRA_CHECKIN_BONUS] = Points.objects.get(action= ActionType.EXTRA_CHECKIN_BONUS).points
            selected_goal.user.points += awarded_points[ActionType.EXTRA_CHECKIN_BONUS]
        else:
            awarded_points[ActionType.DAILY_CHECKIN] = Points.objects.get(action= ActionType.DAILY_CHECKIN).points
            selected_goal.user.points += Points.objects.get(action= ActionType.DAILY_CHECKIN).points

    if selected_goal.streak_awarded or selected_goal.current_checkins != selected_goal.target:
        selected_goal.user.save()
        return awarded_points

    ### Streak rewards ###

    # Weekly streak reward
    awarded_points[ActionType.WEEKLY_STREAK] = Points.objects.get(action= ActionType.WEEKLY_STREAK).points
    selected_goal.user.points += awarded_points[ActionType.WEEKLY_STREAK]
    selected_goal.streak += 1
    selected_goal.streak_awarded = True
    
    # Monthly streak reward
    if selected_goal.streak % 4 == 0:  
        awarded_points[ActionType.MONTHLY_STREAK] = Points.objects.get(action= ActionType.MONTHLY_STREAK).points
        selected_goal.user.points += awarded_points[ActionType.MONTHLY_STREAK]

    # Quarterly streak reward
    if selected_goal.streak % 12 == 0: 
        awarded_points[ActionType.THREE_MONTHS_STREAK] = Points.objects.get(action= ActionType.THREE_MONTHS_STREAK).points
        selected_goal.user.points += awarded_points[ActionType.THREE_MONTHS_STREAK]
        
    # Bi-annual streak reward
    if selected_goal.streak % 24 == 0:
        awarded_points[ActionType.SIX_MONTHS_STREAK] = Points.objects.get(action= ActionType.SIX_MONTHS_STREAK).points
        selected_goal.user.points += awarded_points[ActionType.SIX_MONTHS_STREAK]

    selected_goal.user.save()
    selected_goal.save()

    points_earned = selected_goal.user.points - current_points
    send_target_achievement_notification(selected_goal.user, points_earned)
    return awarded_points

def edit_goal(selected_goal, new_target):
    
    """
    Edit the selected goal to the new goal version

    """

    selected_goal.target = new_target
    previous_streak_status = selected_goal.streak_awarded

    awarded_points = award_points(selected_goal, is_edit_goal=True)

    # Optional: Compare streak status before and after awarding points to indicate FE when to show pop-up
    # Note: streak_awarded can not change to False once True,
    # Hence this condition will apply only if previous streak status was False and now it has chnaged to True
    streak_updated = False
    if previous_streak_status != selected_goal.streak_awarded:
        streak_updated = True

    selected_goal.save()
    
    return {
        "streak_updated": streak_updated, # Optional
        "awarded_points": awarded_points, 
        "assets": [], 
        "badges": [], 
    }

def restore_streak(selected_goal, restore_date):
    """
    Restore the streak for the selected goal

    Args:
        selected_goal (SelectedGoal): The selected goal object.
        restore_date (datetime): The streak restoration date.
    """

    if selected_goal.restore_date:
        days_difference = (restore_date - selected_goal.restore_date).days
        if days_difference <= 6:
            raise PermissionError("Unable to restore streak. Please try again!")
    
    # Check if the user has enough points to restore the streak
    user_points = selected_goal.user.points
    restore_cost = Points.objects.get(action=ActionType.RESTORE_STREAK_COST).points
    if user_points < restore_cost:
        raise ValueError("Not enough points to restore")
    selected_goal.user.points -= restore_cost
    selected_goal.user.save()
    
    # Update the restore week offset to maintain active week count
    if selected_goal.streak_awarded:
        selected_goal.restore_week_offset += selected_goal.current_week
    else:
        selected_goal.restore_week_offset += selected_goal.current_week - 1

    # Reset to new weekly cycle starting from restore date
    selected_goal.current_week = 1

    # Reset other checkpoints
    selected_goal.current_checkins = 0
    selected_goal.streak_awarded = False
    selected_goal.start_date = restore_date
    selected_goal.restore_date = restore_date

    selected_goal.save()

def register_fcm_token(user, fcm_token):
    """
    Register the FCM token for the user or return a list of existing devices.
    Always returns a list of UserDevice objects.
    """
    try:
        if fcm_token:
            UserDevice.objects.get_or_create(user=user, fcm_token=fcm_token)
        devices = UserDevice.objects.filter(user=user)
        return devices
    except IntegrityError:
        raise ValueError("Failed to register device due to a database constraint.")
    except Exception:
        raise ValueError("An unexpected error occurred during device registration.")

def send_welcome_notification(user): 
    """
    Send a welcome notification to a user when they first log in or register.
    """
    notification = Notification.objects.filter(
        sub_type=NotificationSubType.WELCOME_TO_NEUROWORLD
    ).first()

    if not notification:
        raise IntegrityError("Welcome notification template is missing.")

    # Get the user's profile
    user_profile = UserProfile.objects.filter(user=user).first()

    if not user_profile or not user_profile.full_name:
        raise IntegrityError("User profile or full name is missing.")

    # Extract the first name from the full name in the user profile
    first_name = user_profile.full_name.split()[0] if user_profile.full_name else "User"

    # Format the notification body by replacing the placeholder with the first name
    formatted_body = notification.message.replace("[First Name]", first_name)

    # Check if the welcome notification has already been sent to the user
    if UserNotification.objects.filter(user=user, notification=notification).exists():
        return False  # Already sent

    # Create the new UserNotification with the formatted body
    UserNotification.objects.create(
        user=user,
        notification=notification,
        title=notification.title,
        body=formatted_body,  # Use the formatted body here
    )
    return True

def send_target_achievement_notification(user_profile, neuro_points):
    """
    Send a target achievement notification to a user when they achieve their goal.
    """
    user = user_profile.user
    notification = Notification.objects.filter(
        sub_type=NotificationSubType.GOAL_ACHIEVED
    ).first()

    if not notification:
        raise IntegrityError("Target achievement notification template is missing.")

    if not user_profile.full_name:
        raise IntegrityError("User profile or full name is missing.")

    # Extract first name and neuro points
    first_name = user_profile.full_name.split()[0]

    # Format the message
    formatted_body = (
        notification.message
        .replace("[First Name]", first_name)
        .replace("[X]", str(neuro_points))
    )

    # Create the UserNotification (duplicates allowed)
    UserNotification.objects.create(
        user=user,
        notification=notification,
        title=notification.title,
        body=formatted_body,
    )

    return True

def send_goal_setting_encouragement_notification(user):
    """
    Send a goal setting encouragement notification to a user when they set their first goal.
    """
    notification = Notification.objects.filter(
        sub_type=NotificationSubType.START_FIRST_GOAL
    ).first()

    if not notification:
        raise IntegrityError("Goal setting encouragement notification template is missing.")

    # Check if the encouragement notification has already been sent to the user
    if UserNotification.objects.filter(user=user, notification=notification).exists():
        return False  # Already sent

    # Create the new UserNotification with the formatted body
    UserNotification.objects.create(
        user=user,
        notification=notification,
        title=notification.title,
        body=notification.message,  # Use the formatted body here
    )
    return True

def get_api_base_url(request):
    """
    Get the API base URL from the request.
    """
    scheme = request.scheme  # http or https
    host = request.get_host()  # domain + port (if any)
    api_base_url = f"{scheme}://{host}/neuroworld/api"
    return api_base_url

def get_host_url(request):
    """
    Get the app base URL from the request.
    """
    scheme = request.scheme  # http or https
    host = request.get_host()  # domain + port (if any)
    return f"{scheme}://{host}"

def serve_privacy_policy(request):
    
    context = {
        "api_base_url": get_api_base_url(request)
    }
    return render(request, 'privacy-policy.html', context)

def serve_account_deletion(request):
    
    context = {
        "api_base_url": get_api_base_url(request)
    }
    return render(request, 'account-deletion.html', context)

def serve_delete_account_callback(request):
    """
    Serve the delete account callback page.
    """
    return render(request, 'delete-account-callback.html')


async def save_message_to_db(user, user_message=None, assistant_message=None, character= 'Myla', action_type = 'None', actions = []):

    # Create messages
    if user_message:
        await sync_to_async(NeuroChat.objects.create)(
            user=user,
            message=user_message,
            role="user"
        )
    if assistant_message != None:
        if isinstance(assistant_message, str):
            try:
                assistant_message = json.loads(assistant_message)
            except json.JSONDecodeError:
                try:
                    assistant_message = ast.literal_eval(assistant_message)
                except (ValueError, SyntaxError):
                    assistant_message = {
                        'text': assistant_message,
                        'avatar': character,
                        'actionType': action_type,
                        'actions': actions
                    }
        
        if not isinstance(assistant_message, dict):
            assistant_message = {
                'text': str(assistant_message),
                'avatar': character,
                'actionType': action_type,
                'actions': actions
            }
        
        assistant_message.setdefault('text', '')
        assistant_message.setdefault('avatar', 'Myla')
        assistant_message.setdefault('actionType', 'None')
        assistant_message.setdefault('actions', [])
        
        assistant_message_str = json.dumps(assistant_message, ensure_ascii=False)

        assistant_msg_obj = await sync_to_async(NeuroChat.objects.create)(
            user=user,
            message=assistant_message_str,
            role="assistant"
        )
    
        # Return the properly structured assistant data
        return {
            "id": str(assistant_msg_obj.id),
            "message": assistant_message if assistant_message else None,
            "role": assistant_msg_obj.role,
            "date": assistant_msg_obj.date.isoformat(),
            "user": str(assistant_msg_obj.user.id)
        }
    
    return None


def get_apple_public_key(key_id):
    """Fetch Apple's public key for verification"""
    try:
        response = requests.get(settings.APPLE_APP_STORE['PUBLIC_KEYS_URL'])
        keys = response.json().get('keys', [])
        for key in keys:
            if key['kid'] == key_id:
                return key
        return None
    except Exception as e:
        log.error(f"Error fetching Apple public keys: {e}")
        return None

def verify_apple_notification(payload):
    """Verify the authenticity of the Apple notification"""
    try:
        # Verify the signedDate is recent (prevent replay attacks)
        signed_date = datetime.fromtimestamp(payload['signedDate']/1000)
        if (datetime.now() - signed_date).total_seconds() > 3600:  # 1 hour tolerance
            log.warning("Notification is too old")
            return False
            
        # Verify the signedTransactionInfo
        signed_transaction_info = payload['data']['signedTransactionInfo']
        if not verify_signed_data(signed_transaction_info):
            log.warning("Transaction info verification failed")
            return False
            
        # Verify the signedRenewalInfo if present
        if 'signedRenewalInfo' in payload['data']:
            signed_renewal_info = payload['data']['signedRenewalInfo']
            if not verify_signed_data(signed_renewal_info):
                log.warning("Renewal info verification failed")
                return False
                
        return True
    except Exception as e:
        log.error(f"Verification error: {e}")
        return False

def verify_signed_data(signed_data):
    """Verify JWS signed data from Apple"""
    try:
        header = jwt.get_unverified_header(signed_data)
        public_key = get_apple_public_key(header['kid'])
        
        if not public_key:
            log.error("Apple public key not found")
            return False
            
        # Convert JWK to PEM format
        jwk_dict = {
            'kty': public_key['kty'],
            'kid': public_key['kid'],
            'use': public_key['use'],
            'alg': public_key['alg'],
            'n': public_key['n'],
            'e': public_key['e']
        }
        
        # Verify the signature
        decoded = jwt.decode(
            signed_data,
            key=jwk_dict,
            algorithms=['ES256'],
            options={'verify_aud': False, 'verify_iss': False}
        )
        return True
    except Exception as e:
        log.error(f"Signature verification failed: {e}")
        return False

def decode_signed_data(signed_data):
    """Decode JWS signed data without verification (use carefully)"""
    try:
        return jwt.decode(signed_data, options={'verify_signature': False})
    except Exception as e:
        log.error(f"Error decoding signed data: {e}")
        return None

def get_user_from_transaction(transaction_info):
    """
    Map Apple transaction to your user ID
    """    
    try:
        app_account_token = transaction_info.get("appAccountToken")
        if not app_account_token:
            log.error("Missing appAccountToken in transaction info")
            return None

        user = User.objects.filter(id=app_account_token).first()
        if not user:
            log.error(
            f"Subscription Error : User not found for appAccountToken : {app_account_token}",
            extra={'app_account_token': app_account_token}
            )
            return None
        
        return user
    
    except Exception as e:
        log.error(f"Error finding user for transaction {app_account_token}: {e}")
        return None