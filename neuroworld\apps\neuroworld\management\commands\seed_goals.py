import json
import os
from django.core.management.base import BaseCommand
from django.db.utils import IntegrityError
from apps.neuroworld.models import Goal, GoalCategory, GoalVersion, GoalIntensity

class Command(BaseCommand):
    help = "Seed Goal records with default values, including Goal Versions"

    def handle(self, *args, **kwargs):
        """
        Seed the database with Goal and GoalVersion entries from the JSON file.
        """
        file_path = os.path.join(os.path.dirname(__file__), "../../fixtures/goal_data.json")

        try:
            with open(file_path, "r", encoding="utf-8") as file:
                goals_data = json.load(file)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error reading JSON file: {str(e)}"))
            return

        for goal_data in goals_data:
            title = goal_data["title"]
            image_url = goal_data["image_url"]
            category_name = goal_data["category"]
            description = goal_data.get("description", "")
            verb = goal_data.get("verb", "")

            # Ensure category is correctly mapped from string to GoalCategory enum
            try:
                category = getattr(GoalCategory, category_name)
            except AttributeError:
                self.stdout.write(self.style.ERROR(f"Invalid category '{category_name}' for goal '{title}'"))
                continue

            try:
                goal_obj, created = Goal.objects.get_or_create(
                    title=title,
                    category=category,
                    defaults={"image_url": image_url, "description": description, "verb": verb}
                )
                if not created:
                    goal_obj.image_url = image_url
                    goal_obj.description = description
                    goal_obj.verb = verb
                    goal_obj.save()

                # Create GoalVersion entries for both Beginner and Advanced
                for level in GoalIntensity.values:
                    try:
                        GoalVersion.objects.get_or_create(
                            goal=goal_obj,
                            level=level,
                            defaults={"description": f"{title} - {level}"}
                        )
                    except IntegrityError as e:
                        self.stdout.write(self.style.ERROR(f"Integrity error for GoalVersion '{title} - {level}': {str(e)}"))
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f"Error creating GoalVersion '{title} - {level}': {str(e)}"))

            except IntegrityError as e:
                self.stdout.write(self.style.ERROR(f"Integrity error for Goal '{title}': {str(e)}"))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error creating/updating Goal '{title}': {str(e)}"))

        self.stdout.write(self.style.SUCCESS("Seeded Goal and GoalVersion records successfully."))
