from django.core.management.base import BaseCommand
from apps.neuroworld.models import GoalCategory, Badge, BadgeType, BadgeLevel

class Command(BaseCommand):
    help = 'Initialize all badges in the system'

    def handle(self, *args, **options):
        self.create_streak_badges()
        # self.create_city_badges()
        self.stdout.write(self.style.SUCCESS('Successfully initialized badges'))

    def create_streak_badges(self):
        # Streak badges for each category
        categories = GoalCategory.choices
        streak_config = [
            (BadgeLevel.BRONZE, 1, "one-week streak"),
            (BadgeLevel.SILVER, 4, "one-month streak"),
            (BadgeLevel.GOLD, 12, "three-month streak"),
            (BadgeLevel.SAPPHIRE, 24, "six-month streak"),
            (BadgeLevel.DIAMOND, 36, "nine-month streak on 3 habits"),
            (BadgeLevel.PLATINUM, 48, "one-year streak on 3 habits"),
        ]
        
        for category in categories:
            category_value, category_label = category
            for level, days, desc in streak_config:
                required_habits = 3 if level in [BadgeLevel.DIAMOND, BadgeLevel.PLATINUM] else 1
                title = f"{category_label.split()[0].title()} {level.title()}"
                Badge.objects.get_or_create(
                    title=title,
                    defaults={
                        'description': f"Achieved a {desc} on {category_label} habits",
                        'badge_type': BadgeType.STREAK,
                        'level': level,
                        'category': category_value,
                        'required_streak': days,
                        'required_habits': required_habits,
                        'image_url': f"{category_value}_{level}",
                    }
                )
