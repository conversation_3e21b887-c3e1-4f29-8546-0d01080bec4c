import logging
from datetime import datetime, timedelta
from django.db import IntegrityError, transaction
from django.utils import timezone
from .models import User, UserSubscription, UserTransaction, SubscriptionPlan, TransactionType

logger = logging.getLogger(__name__)


def get_user_from_transaction(transaction_info):
    """
    Helper function to get user from transaction info.
    Implement your own logic based on how you associate users with purchases.
    
    Common approaches:
    1. Store original_transaction_id in user profile during initial purchase
    2. Include user ID in the appAccountToken field when making purchases
    3. Other app-specific association method
    
    Args:
        transaction_info: Decoded transaction info from Apple
    
    Returns:
        User instance or None if not found
    """
    # Example implementation - adjust based on your app's logic
    original_transaction_id = transaction_info.get('originalTransactionId')
    if not original_transaction_id:
        return None

    try:
        # Option 1: Look for existing subscription with this transaction ID
        subscription = UserSubscription.objects.filter(
            purchase_token=original_transaction_id
        ).first()
        
        if subscription:
            return subscription.user
        
        return None
        
    except User.DoesNotExist:
        return None

class PurchaseProcessor:
    """
    Handles Apple In-App Purchase notifications and updates user subscriptions accordingly.
    Follows Django best practices with proper error handling, logging, and atomic transactions.
    """

    def __init__(self, user, event_data, product_id=None):
        """
        Initialize the processor with user, event data, and optional product ID.
        
        Args:
            user: Django User instance
            event_data: Dictionary containing Apple notification data
            product_id: Optional product ID if not present in event_data
        """
        self.user = user
        self.event_data = event_data
        self.product_id = product_id or self._get_product_id_from_event()
        self.original_transaction_id = event_data.get('originalTransactionId')
        self.transaction_id = event_data.get('transactionId')
        
        # Validate required fields
        if not self.original_transaction_id:
            raise ValueError("Missing originalTransactionId in event data")
        if not self.transaction_id:
            raise ValueError("Missing transactionId in event data")
        if not self.product_id:
            raise ValueError("Product ID not provided and not found in event data")

    def _get_product_id_from_event(self):
        """Extract product ID from event data if available."""
        return self.event_data.get('productId')

    def process_event(self):
        """
        Route the event to the appropriate handler based on notification type.
        """
        notification_type = self.event_data.get('notificationType')
        subtype = self.event_data.get('subtype')

        logger.info(
            # f"Processing Apple IAP event for user {self.user.id}. "
            f"Type: {notification_type}, Subtype: {subtype}, "
            f"Product: {self.product_id}"
        )

        # Map notification types to handler methods
        handlers = {
            'SUBSCRIBED': {
                'INITIAL_BUY': self.handle_initial_subscription,
                None: self.handle_initial_subscription,
            },
            'DID_RENEW': {
                None: self.handle_renewal,
            },
            'DID_FAIL_TO_RENEW': {
                None: self.handle_renewal_failure,
            },
            'DID_CHANGE_RENEWAL_STATUS': {
                'AUTO_RENEW_DISABLED': self.handle_auto_renew_disabled,
                'AUTO_RENEW_ENABLED': self.handle_auto_renew_enabled,  # Handle both enable/disable
            },
            'EXPIRED': {
                None: self.handle_expiration,
                'VOLUNTARY': self.handle_expiration,  # Handle voluntary expiration
            },
        }

        try:
            handler = handlers.get(notification_type, {}).get(subtype)
            if handler:
                return handler()
            raise ValueError(
                f"No handler for notification type {notification_type} "
                f"with subtype {subtype}"
            )
        except IntegrityError:
            # Ignore integrity errors as the subscription is already created
            pass
        except Exception as e:
            logger.error(
                f"Error processing Apple IAP event for user {self.user.id}: {str(e)}",
                exc_info=True
            )
            raise

    @transaction.atomic
    def handle_initial_subscription(self):
        """
        Handle initial subscription purchase (INITIAL_BUY).
        Creates a new subscription and logs the initial transaction.
        """
        # Get the subscription plan
        try:
            plan = SubscriptionPlan.objects.get(product_id=self.product_id)
        except SubscriptionPlan.DoesNotExist:
            raise ValueError(f"Subscription plan with product_id {self.product_id} not found")

        # Parse Apple's date format
        purchase_date = self._parse_apple_date(self.event_data.get('purchaseDate'))
        expiry_date = self._parse_apple_date(self.event_data.get('expiresDate'))

        # Create or update the subscription
        subscription, created = UserSubscription.objects.update_or_create(
            purchase_token=self.original_transaction_id,
            defaults={
                'user': self.user,
                'plan': plan,
                'status': 'active',
                'start_time': purchase_date,
                'expiry_time': expiry_date,
                'is_auto_renewing': True,
                'is_cancelled': False,
                'is_expired': False,
                'platform': 'apple',
            }
        )

        # Log the transaction
        UserTransaction.objects.create(
            user=self.user,
            subscription=subscription,
            transaction_id=self.transaction_id,
            original_transaction_id=self.original_transaction_id,
            transaction_type=TransactionType.INITIAL_PURCHASE,
            amount=plan.price,
            transaction_time=purchase_date,
            product_id=self.product_id,
            platform='apple',
            raw_data=self.event_data,
        )

        logger.info(
            f"Created new subscription for user {self.user.id}. "
            f"Plan: {plan.name}, Expires: {expiry_date}"
        )
        return subscription

    @transaction.atomic
    def handle_renewal(self):
        """
        Handle subscription renewal (DID_RENEW).
        Updates the subscription expiry date and logs the renewal transaction.
        """
        try:
            subscription = UserSubscription.objects.get(
                purchase_token=self.original_transaction_id,
                user=self.user
            )
        except UserSubscription.DoesNotExist:
            raise ValueError(
                f"Subscription with originalTransactionId {self.original_transaction_id} "
                f"not found for user {self.user.id}"
            )

        # Parse dates from Apple's format
        purchase_date = self._parse_apple_date(self.event_data.get('purchaseDate'))
        expiry_date = self._parse_apple_date(self.event_data.get('expiresDate'))

        # Update subscription
        subscription.expiry_time = expiry_date
        subscription.is_auto_renewing = True
        subscription.is_expired = False
        subscription.status = 'active'
        subscription.save()

        # Log the transaction
        UserTransaction.objects.create(
            user=self.user,
            subscription=subscription,
            transaction_id=self.transaction_id,
            original_transaction_id=self.original_transaction_id,
            transaction_type=TransactionType.RENEWAL,
            amount=subscription.plan.price,
            transaction_time=purchase_date,
            product_id=self.product_id,
            platform='apple',
            raw_data=self.event_data,
        )

        logger.info(
            f"Renewed subscription for user {self.user.id}. "
            f"New expiry: {expiry_date}"
        )
        return subscription

    @transaction.atomic
    def handle_renewal_failure(self):
        """
        Handle failed renewal (DID_FAIL_TO_RENEW).
        Marks the subscription as needing attention but keeps it active until expiry.
        """
        try:
            subscription = UserSubscription.objects.get(
                purchase_token=self.original_transaction_id,
                user=self.user
            )
        except UserSubscription.DoesNotExist:
            raise ValueError(
                f"Subscription with originalTransactionId {self.original_transaction_id} "
                f"not found for user {self.user.id}"
            )

        # Update subscription status
        subscription.is_auto_renewing = False
        subscription.status = 'pending_failure'
        subscription.save()

        # Log the transaction
        UserTransaction.objects.create(
            user=self.user,
            subscription=subscription,
            transaction_id=self.transaction_id,
            original_transaction_id=self.original_transaction_id,
            transaction_type=TransactionType.CANCEL,
            amount=subscription.plan.price,
            transaction_time=timezone.now(),
            product_id=self.product_id,
            platform='apple',
            raw_data=self.event_data,
        )

        logger.warning(
            f"Renewal failed for user {self.user.id}. "
            f"Subscription will expire on {subscription.expiry_time}"
        )
        return subscription

    @transaction.atomic
    def handle_auto_renew_status_change(self, is_auto_renew_enabled, transaction_type):
        """
        Generic handler for auto-renew status changes.
        Updates the subscription's auto-renew state and logs the transaction.
        """
        try:
            subscription = UserSubscription.objects.get(
                purchase_token=self.original_transaction_id,
                user=self.user
            )
            logger.info(
                f"Handling auto-renew status change for user {self.user.id}. "
                f"Subscription: {subscription}, "
            )
        except UserSubscription.DoesNotExist:
            raise ValueError(
                f"Subscription with originalTransactionId {self.original_transaction_id} "
                f"not found for user {self.user.id}"
            )

        # Update subscription state
        subscription.is_auto_renewing = is_auto_renew_enabled
        subscription.is_cancelled = not is_auto_renew_enabled
        subscription.status = 'active' if is_auto_renew_enabled else 'cancelled'
        subscription.save()

        # Log the transaction
        UserTransaction.objects.create(
            user=self.user,
            subscription=subscription,
            transaction_id=self.transaction_id,
            original_transaction_id=self.original_transaction_id,
            transaction_type=transaction_type,
            amount=subscription.plan.price,
            transaction_time=timezone.now(),
            product_id=self.product_id,
            platform='apple',
            raw_data=self.event_data,
        )

        status_text = 'enabled' if is_auto_renew_enabled else 'disabled'
        logger.info(
            f"Auto-renew {status_text} for user {self.user.id}. "
            f"Subscription {'continues' if is_auto_renew_enabled else f'will expire on {subscription.expiry_time}'}"
        )

        return subscription

    @transaction.atomic
    def handle_auto_renew_disabled(self):
        """
        Wrapper for handling auto-renew being disabled.
        """
        return self.handle_auto_renew_status_change(
            is_auto_renew_enabled=False,
            transaction_type=TransactionType.CANCEL
        )

    @transaction.atomic
    def handle_auto_renew_enabled(self):
        """
        Wrapper for handling auto-renew being re-enabled.
        """
        return self.handle_auto_renew_status_change(
            is_auto_renew_enabled=True,
            transaction_type=TransactionType.RENEWAL
        )

    @transaction.atomic
    def handle_expiration(self):
        """
        Handle subscription expiration (EXPIRED).
        Marks the subscription as expired.
        """
        try:
            subscription = UserSubscription.objects.get(
                purchase_token=self.original_transaction_id,
                user=self.user
            )
        except UserSubscription.DoesNotExist:
            raise ValueError(
                f"Subscription with originalTransactionId {self.original_transaction_id} "
                f"not found for user {self.user.id}"
            )

        # Update subscription
        subscription.is_expired = True
        subscription.status = 'expired'
        subscription.is_auto_renewing = False
        subscription.expiry_time = timezone.now()  # Set to now if not provided
        subscription.save()

        # Log the transaction
        UserTransaction.objects.create(
            user=self.user,
            subscription=subscription,
            transaction_id=self.transaction_id,
            original_transaction_id=self.original_transaction_id,
            transaction_type=TransactionType.REVOKE,
            amount=subscription.plan.price,
            transaction_time=timezone.now(),
            product_id=self.product_id,
            platform='apple',
            raw_data=self.event_data,
        )

        logger.info(f"Subscription expired for user {self.user.id}")
        return subscription

    def _parse_apple_date(self, date_str):
        """
        Parses Apple-style date strings or timestamps into a timezone-aware datetime object.

        Supports:
        - Milliseconds since epoch (e.g., 1750164281000)
        - ISO 8601 format (e.g., '2020-06-17 10:56:27+00:00')
        - ISO 8601 without timezone (e.g., '2020-06-17 10:56:27')

        Returns:
            A timezone-aware datetime object or None if input is invalid.
        """
        if date_str is None:
            return None

        try:
            # If it's a timestamp in milliseconds (int or numeric string)
            if isinstance(date_str, int) or (isinstance(date_str, str) and date_str.isdigit()):
                timestamp = int(date_str) / 1000.0  # Convert to seconds
                return datetime.fromtimestamp(timestamp, tz=timezone.utc)

            # Handle ISO format with timezone
            return datetime.fromisoformat(date_str).astimezone(timezone.utc)

        except ValueError:
            try:
                # Fallback: naive datetime without timezone
                naive_dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                return naive_dt.replace(tzinfo=timezone.utc)
            except Exception as e:
                logger.error(f"Failed to parse Apple date string: {date_str}")
                raise ValueError(f"Invalid date format: {date_str}") from e
            

class GooglePurchaseProcessor:
    def __init__(self, user, subscription_details, notification_type, purchase_token):
        self.user = user
        self.subscription_details = subscription_details
        self.notification_type = notification_type
        self.purchase_token = purchase_token

        try:
            self.line_item = subscription_details.get('lineItems', [{}])[0]
            self.product_id = self.line_item.get('productId')
            self.order_id = subscription_details.get('latestOrderId')
            self.start_time = self.parse_google_date(subscription_details.get('startTime'))
            self.expiry_time = self.parse_google_date(self.line_item.get('expiryTime'))
            self.plan = SubscriptionPlan.objects.filter(product_id=self.product_id).first()
            auto_renewing_plan = self.line_item.get('autoRenewingPlan', {})
           
            self.recurring_price = auto_renewing_plan.get('recurringPrice', {})
            self.currency = self.recurring_price.get('currencyCode', 'USD')
            self.amount = self.recurring_price.get('units', 0)

            is_auto_renewing =  auto_renewing_plan.get('autoRenewEnabled', False)
            self.auto_renew_enabled = is_auto_renewing or bool(self.recurring_price)

            if not self.plan:
                raise ValueError(f"Subscription plan with product_id {self.product_id} not found")

        except Exception as e:
            logger.exception(f"Error initializing GooglePurchaseProcessor: {str(e)}")
            raise

    def process_event(self):
        """
        Route notification types to appropriate handlers.
        """
        handlers = {
            4: self.handle_initial_purchase,  # SUBSCRIPTION_PURCHASED
            2: self.handle_renewal,           # SUBSCRIPTION_RENEWED
            3: self.handle_cancellation,      # SUBSCRIPTION_CANCELED
            12: self.handle_revocation,       # SUBSCRIPTION_REVOKED
            13: self.handle_expiration,       # SUBSCRIPTION_EXPIRED
        }

        handler = handlers.get(self.notification_type)

        if handler:
            try:
                logger.info(f"Processing Google subscription event for user {self.user.id}, Type: {self.notification_type}")
                return handler()
            except Exception as e:
                logger.exception(f"Error processing event for user {self.user.id}: {str(e)}")
                return None
        else:
            logger.warning(f"Unhandled Google notification type: {self.notification_type}")
            return None

    def create_transaction(self, subscription, transaction_type):
        """
        Create a UserTransaction record with exception handling.
        """
        
        try:
            UserTransaction.objects.create(
                user=self.user,
                subscription=subscription,
                transaction_id=self.order_id,
                original_transaction_id=self.purchase_token,
                transaction_type=transaction_type,
                amount=self.amount or subscription.plan.price,
                currency=self.currency,
                transaction_time=self.start_time or timezone.now(),
                product_id=self.product_id,
                platform='google',
                raw_data=self.subscription_details,
            )

            logger.info(f"Transaction created: {transaction_type} for user {self.user.id}")

        except IntegrityError:
            logger.warning(f"Duplicate transaction detected for token: {self.purchase_token}")
        except Exception as e:
            logger.exception(f"Error creating transaction: {str(e)}")

    @transaction.atomic
    def handle_initial_purchase(self):
        """
        Handle new subscription purchases.
        """
        subscription, created = UserSubscription.objects.update_or_create(
            purchase_token=self.purchase_token,
            defaults={
                'user': self.user,
                'plan': self.plan,
                'status': 'active',
                'start_time': self.start_time,
                'expiry_time': self.expiry_time,
                'is_auto_renewing': self.auto_renew_enabled,
                'is_cancelled': False,
                'is_expired': False,
                'platform': 'google',
            }
        )

        self.create_transaction(subscription, TransactionType.INITIAL_PURCHASE)
        logger.info(f"Created new Google subscription for user {self.user.id}")
        return subscription

    @transaction.atomic
    def handle_renewal(self):
        """
        Handle subscription renewals.
        """
        subscription = UserSubscription.objects.filter(
            purchase_token=self.purchase_token,
            user=self.user
        ).first()

        if not subscription:
            logger.warning(f"Renewal failed: Subscription not found for user {self.user.id} with token {self.purchase_token}")
            return None

        subscription.expiry_time = self.expiry_time
        subscription.is_auto_renewing = self.auto_renew_enabled
        subscription.is_expired = False
        subscription.status = 'active'
        subscription.save()

        self.create_transaction(subscription, TransactionType.RENEWAL)
        logger.info(f"Renewed Google subscription for user {self.user.id}")
        return subscription

    @transaction.atomic
    def handle_cancellation(self):
        """
        Handle subscription cancellations.
        """
        subscription = UserSubscription.objects.filter(
            purchase_token=self.purchase_token,
            user=self.user
        ).first()

        if not subscription:
            logger.warning(f"Cancellation failed: Subscription not found for user {self.user.id} with token {self.purchase_token}")
            return None

        subscription.is_auto_renewing = False
        subscription.is_cancelled = True
        subscription.status = 'cancelled'
        subscription.save()

        self.create_transaction(subscription, TransactionType.CANCEL)
        logger.info(f"Cancelled Google subscription for user {self.user.id}")
        return subscription

    @transaction.atomic
    def handle_revocation(self):
        """
        Handle subscription revocations.
        """
        return self.handle_cancellation()

    @transaction.atomic
    def handle_expiration(self):
        """
        Handle subscription expirations.
        """
        subscription = UserSubscription.objects.filter(
            purchase_token=self.purchase_token,
            user=self.user
        ).first()

        if not subscription:
            logger.warning(f"Expiration failed: Subscription not found for user {self.user.id} with token {self.purchase_token}")
            return None

        subscription.is_expired = True
        subscription.status = 'expired'
        subscription.is_auto_renewing = False
        subscription.expiry_time = timezone.now()
        subscription.save()

        self.create_transaction(subscription, TransactionType.REVOKE)
        logger.info(f"Expired Google subscription for user {self.user.id}")
        return subscription

    def parse_google_date(self, date_str):
        """
        Convert Google date string to timezone-aware datetime.
        """
        if not date_str:
            return None

        try:
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except Exception as e:
            logger.error(f"Failed to parse Google date string: {date_str}")
            return None