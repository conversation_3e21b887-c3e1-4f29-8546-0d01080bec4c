import os
from celery import Celery

# Set default Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neuroworld.settings')  # replace with your project name

app = Celery('neuroworld')

# Load config from Django settings, using CELERY_ prefix
app.config_from_object('django.conf:settings', namespace='CELERY')

# Auto-discover tasks across installed apps
app.autodiscover_tasks()

# # Define your custom queues
# app.conf.task_queues = (
#     Queue('neuro-morning-notifications'),
#     Queue('neuro-midday-notifications'),
#     Queue('neuro-evening-notifications'),
#     Queue('neuro-profile-completion-notifications'),
#     # Queue('neuro-missed-streak-notifications'),
# )

# # Optional: set default queue
# app.conf.task_default_queue = 'neuro-morning-notifications'

# Transport options for Amazon SQS
app.conf.broker_transport_options = {
    'region': 'us-east-1',  # replace with your actual AWS region
    'visibility_timeout': 3600,
    'polling_interval': 5,
}
