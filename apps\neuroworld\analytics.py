"""
PostHog Analytics Service for NeuroWorld

This module provides a centralized service for tracking user events and properties
using PostHog analytics. It implements the Query-as-Needed approach where:
- Event data is stored in PostHog
- Structured data remains in PostgreSQL
- Data is queried on-demand when needed
"""

import logging
from typing import Dict, Any, Optional, Union
from datetime import datetime, date

import posthog
from django.conf import settings
from django.contrib.auth import get_user_model

from .models import UserProfile

logger = logging.getLogger(__name__)
User = get_user_model()


class AnalyticsService:
    """
    Centralized service for PostHog analytics integration.
    
    Handles user identification, event tracking, and property management
    for the NeuroWorld application.
    """
    
    def __init__(self):
        """Initialize PostHog client with configuration from Django settings."""
        if settings.POSTHOG_ENABLED and settings.POSTHOG_API_KEY:
            posthog.api_key = settings.POSTHOG_API_KEY
            posthog.host = settings.POSTHOG_HOST
            self.enabled = True
            logger.info("PostHog analytics initialized successfully")
        else:
            self.enabled = False
            logger.warning("PostHog analytics disabled - missing configuration")
    
    def _get_user_properties(self, user: User) -> Dict[str, Any]:
        """
        Extract user properties for PostHog identification.
        
        Args:
            user: Django User instance
            
        Returns:
            Dictionary of user properties for PostHog
        """
        properties = {
            'email': user.email,
            'username': user.username or '',
            'date_joined': user.date_joined.isoformat() if user.date_joined else None,
            'auth_provider': user.auth_provider,
            'is_staff': user.is_staff,
            'is_active': user.is_active,
        }
        
        # Add UserProfile properties if available
        try:
            profile = user.userprofile
            
            # Split full_name into first_name and last_name
            if profile.full_name:
                name_parts = profile.full_name.strip().split(' ', 1)
                properties['first_name'] = name_parts[0]
                properties['last_name'] = name_parts[1] if len(name_parts) > 1 else ''
            else:
                properties['first_name'] = ''
                properties['last_name'] = ''
            
            # Add other profile properties
            properties.update({
                'gender': profile.gender or '',
                'date_of_birth': profile.birth_date.isoformat() if profile.birth_date else None,
                'points': profile.points,
                'is_onboarding_completed': profile.is_onboarding_completed,
                'is_profile_completed': profile.is_profile_completed,
                'time_zone': profile.time_zone,
                'is_chat_consent': profile.is_chat_consent,
            })
            
        except UserProfile.DoesNotExist:
            logger.warning(f"UserProfile not found for user {user.id}")
            properties.update({
                'first_name': '',
                'last_name': '',
                'gender': '',
                'date_of_birth': None,
                'points': 0,
                'is_onboarding_completed': False,
                'is_profile_completed': False,
            })
        
        return properties
    
    def identify_user(self, user: User) -> bool:
        """
        Identify a user in PostHog with their properties.
        
        Args:
            user: Django User instance
            
        Returns:
            True if successful, False otherwise
        """
        if not self.enabled:
            return False
        
        try:
            user_id = str(user.id)
            properties = self._get_user_properties(user)
            
            posthog.identify(
                distinct_id=user_id,
                properties=properties
            )
            
            logger.info(f"User {user_id} identified in PostHog")
            return True
            
        except Exception as e:
            logger.error(f"Failed to identify user {user.id} in PostHog: {e}")
            return False
    
    def track_event(self, user: User, event_name: str, properties: Optional[Dict[str, Any]] = None) -> bool:
        """
        Track an event for a user in PostHog.
        
        Args:
            user: Django User instance
            event_name: Name of the event to track
            properties: Additional event properties (optional)
            
        Returns:
            True if successful, False otherwise
        """
        if not self.enabled:
            return False
        
        try:
            user_id = str(user.id)
            event_properties = properties or {}
            
            # Add timestamp
            event_properties['timestamp'] = datetime.now().isoformat()
            
            posthog.capture(
                distinct_id=user_id,
                event=event_name,
                properties=event_properties
            )
            
            logger.info(f"Event '{event_name}' tracked for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to track event '{event_name}' for user {user.id}: {e}")
            return False
    
    def update_user_properties(self, user: User, properties: Dict[str, Any]) -> bool:
        """
        Update user properties in PostHog.
        
        Args:
            user: Django User instance
            properties: Properties to update
            
        Returns:
            True if successful, False otherwise
        """
        if not self.enabled:
            return False
        
        try:
            user_id = str(user.id)
            
            posthog.identify(
                distinct_id=user_id,
                properties=properties
            )
            
            logger.info(f"Properties updated for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update properties for user {user.id}: {e}")
            return False
    
    def track_user_registration(self, user: User) -> bool:
        """Track user registration event."""
        return self.track_event(
            user=user,
            event_name='User Registered',
            properties={
                'auth_provider': user.auth_provider,
                'registration_date': user.date_joined.isoformat() if user.date_joined else None,
            }
        )
    
    def track_user_login(self, user: User, auth_provider: str = None) -> bool:
        """Track user login event."""
        return self.track_event(
            user=user,
            event_name='User Logged In',
            properties={
                'auth_provider': auth_provider or user.auth_provider,
                'login_time': datetime.now().isoformat(),
            }
        )
    
    def track_goal_tracking(self, user: User, goal_name: str, tracking_date: date) -> bool:
        """Track goal tracking event."""
        return self.track_event(
            user=user,
            event_name='Goal Tracked',
            properties={
                'goal_name': goal_name,
                'tracking_date': tracking_date.isoformat(),
            }
        )
    
    def track_profile_update(self, user: User, updated_fields: list) -> bool:
        """Track profile update event."""
        return self.track_event(
            user=user,
            event_name='Profile Updated',
            properties={
                'updated_fields': updated_fields,
            }
        )


# Global analytics service instance
analytics = AnalyticsService()
