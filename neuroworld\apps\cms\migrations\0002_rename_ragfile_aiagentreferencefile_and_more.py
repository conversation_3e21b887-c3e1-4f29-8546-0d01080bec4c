# Generated by Django 4.2.18 on 2025-06-19 19:10

from django.db import migrations, models
import wagtail.fields


class Migration(migrations.Migration):

    dependencies = [
        ('cms', '0001_initial'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='RAGFile',
            new_name='AIAgentReferenceFile',
        ),
        migrations.AddField(
            model_name='character',
            name='few_shots_input',
            field=wagtail.fields.StreamField([('shot', 0)], blank=True, block_lookup={0: ('wagtail.blocks.CharBlock', (), {'label': 'Few-Shot Example'})}, verbose_name='Few-Shot Examples Input'),
        ),
        migrations.AlterField(
            model_name='character',
            name='few_shots',
            field=models.JSONField(blank=True, default=list, verbose_name='Few-Shot Examples'),
        ),
    ]
