# Generated by Django 4.2.18 on 2025-06-19 08:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('neuroworld', '0036_ragfile'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='ragfile',
            name='file',
        ),
        migrations.RemoveField(
            model_name='ragfile',
            name='s3_url',
        ),
        migrations.AddField(
            model_name='ragfile',
            name='s3_object_key',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='S3 Object Key'),
        ),
        migrations.AddField(
            model_name='ragfile',
            name='temp_file',
            field=models.FileField(blank=True, null=True, upload_to='temp/', verbose_name='Upload File'),
        ),
    ]
