import logging
import pytz
import random
import json
import boto3
from django.conf import settings
from django.utils import timezone
import time
from asgiref.sync import sync_to_async

from .firebase_service import fcm_send_with_retry
from .sqs_service import delete_sqs_message, get_queue_url, receive_sqs_messages
from ..models import Notification, NotificationSubType, NotificationType, SelectedGoal, UserDevice, UserNotification, UserProfile
from django.db.models import Q

logger = logging.getLogger(__name__)

# General utility functions for notifications
def get_tz_coordinated_time(tz_name):
    try:
        tz = get_timezone(tz_name)
        now_utc = timezone.now()
        now_tz = now_utc.astimezone(tz)
        return now_utc, now_tz
    except pytz.UnknownTimeZoneError:
        logger.warning(f"Invalid timezone: {tz_name}")
        return None, None

def get_distinct_timezones():
    return UserProfile.objects.exclude(time_zone=None).values_list('time_zone', flat=True).distinct()

def get_timezone(tz_name: str):
    """Get the timezone object with fallback to UTC."""
    try:
        return pytz.timezone(tz_name)
    except pytz.UnknownTimeZoneError:
        return pytz.UTC

def get_jittered_target_time(base_time, hour, minute=0, jitter_range=(-20, 20)):
    """Return jittered target time around give time."""
    jitter_minutes = random.randint(*jitter_range)
    target_time = base_time.replace(hour=hour, minute=minute, second=0, microsecond=0) + timezone.timedelta(minutes=jitter_minutes)
    return target_time

def get_user_devices(user_ids):
    """Return user devices based on user IDs."""
    return UserDevice.objects.filter(user__id__in=user_ids).select_related('user__userprofile')

def get_notification_template(notif_type, notif_sub_type):
    """Fetch the notification template based on type and sub-type."""
    return Notification.objects.filter(
        type=notif_type,
        sub_type=notif_sub_type,
    ).values('title', 'message', 'type', 'sub_type', 'icon').first()

def batch_notifications(user_tokens, window_name, scheduled_time, timezone='UTC'):
    batch_size = settings.FCM_BATCH_SIZE
    sqs = boto3.client('sqs', region_name=settings.AWS_REGION)
    queue_name = settings.NOTIFICATION_SCHEDULE[window_name]['sqs_queue']

    all_tokens = [token for data in user_tokens.values() for token in data['tokens']]

    for i in range(0, len(all_tokens), batch_size):
        batch_tokens = set(all_tokens[i:i + batch_size])

        logger.info(f"[{window_name}] Processing batch {i // batch_size + 1} with {len(batch_tokens)} tokens.")
        
        user_messages = []
        for uid, data in user_tokens.items():
            matching_tokens = list(batch_tokens.intersection(data['tokens']))
            if matching_tokens:
                user_messages.append({
                    'user_id': str(uid),
                    'message': data['message'],
                    'notification_id': data['notification_id'],
                    'tokens': matching_tokens
                })

        payload = {
            'window': window_name,
            'timezone': timezone,
            'scheduled_time': scheduled_time.isoformat(),
            'user_messages': user_messages,
        }

        logger.info(f"[{window_name}] Sending user messages: {user_messages}")

        logger.info(f"[{window_name}] Sending user messages: {user_messages}")

        send_sqs_message(
            sqs_client=sqs,
            queue_name=queue_name,
            message_body=json.dumps(payload),
            window_name=window_name
        )

def send_sqs_message(sqs_client, queue_name, message_body, window_name):
    """Robust SQS message sender with error handling and retries."""

    logger.info(f"[{window_name}] Sending message to SQS queue: {queue_name}")

    max_retries = 3
    for attempt in range(max_retries):
        try:
            queue_url = get_queue_url(queue_name)
            response = sqs_client.send_message(
                QueueUrl=queue_url,
                MessageBody=message_body,
                MessageAttributes={
                    'NotificationType': {
                        'DataType': 'String',
                        'StringValue': queue_name.split('-')[1]
                    }
                }
            )
            return response['MessageId']
        except sqs_client.exceptions.QueueDoesNotExist:
            logger.warning(f"[{window_name}] SQS queue {queue_name} does not exist. Attempting to create it.")
            sqs_client.create_queue(QueueName=queue_name)
            continue
        except Exception as e:
            
            if attempt == max_retries - 1:
                logger.error(f"[{window_name}] Failed to send message to SQS after {max_retries} attempts: {e}", exc_info=True)
                raise

            delay = 2 ** attempt  # Exponential backoff
            logger.info(f"[{window_name}] Error sending message to SQS: {e}. Retrying in {delay} seconds...")
            time.sleep(2 ** attempt)  # Exponential backoff

# Utility function for schedule_profile_completion_reminder
def get_users_for_incomplete_profiles(cutoff_date):
    """
    Returns users with truly incomplete profiles and due reminders.
    Also updates is_profile_completed=True for those who already meet profile completion criteria.
    """
    # Step 1: Correct faulty records
    faulty_profiles_qs = UserProfile.objects.filter(
        is_profile_completed=False,
        full_name__isnull=False,
        gender__isnull=False,
        birth_date__isnull=False
    )

    # Apply empty string exclusion if needed
    faulty_profiles_qs = faulty_profiles_qs.exclude(full_name='')

    updated_count = faulty_profiles_qs.update(is_profile_completed=True)
    if updated_count > 0:
        logger.info(f"[get_users_for_incomplete_profiles] Updated {updated_count} incorrectly marked profiles to completed.")

    # Step 2: Return only incomplete profiles with reminder due
    return UserProfile.objects.filter(
        is_profile_completed=False
    ).filter(
        Q(profile_completion_reminder_date__lte=cutoff_date) | Q(profile_completion_reminder_date__isnull=True)
    ).select_related('user')

# Utility function for schedule_streak_reminders
def get_users_for_streak_reminders(now_utc, task_name):
    """Get users eligible for streak reminders."""

    # 1. Get goals where streak is not awarded
    eligible_goals = SelectedGoal.objects.filter(
            streak_awarded=False,
        ).select_related('user', 'user__user')

    if not eligible_goals.exists():
        logger.info(f"[{task_name}] No eligible goals found.")
        return

    filtered_goals = []
    processed_user_ids = set()

    # 2. Filter goals that fall within the 14-day restore period
    for goal in eligible_goals:
        user = goal.user
        user_id = user.id

        # Skip if we've already processed a goal for this user
        if user_id in processed_user_ids:
            continue

        user_tz_str = getattr(user, 'time_zone', 'UTC')
        user_tz = get_timezone(user_tz_str)
        now_user_tz = now_utc.astimezone(user_tz).date()

        start_date = goal.start_date
        if not start_date:
            continue

        week_end_date = start_date + timezone.timedelta(days=6)
        upper_limit = week_end_date + timezone.timedelta(days=14)

        if not (week_end_date < now_user_tz <= upper_limit):
            continue

        if user.streak_reminder_date:
            days_diff = (now_utc.date() - user.streak_reminder_date.date()).days
            if days_diff != 1:
                continue

        filtered_goals.append(goal)
        processed_user_ids.add(user_id)
    return filtered_goals

# Utility functions for process_sqs_queues
def get_all_sqs_queues():
    """Extract all (window_name, sqs_queue) tuples from settings.NOTIFICATION_SCHEDULE."""
    return [
        (k, v['sqs_queue']) 
        for k, v in settings.NOTIFICATION_SCHEDULE.items() 
        if 'sqs_queue' in v
    ]


async def _get_base_notification(notification_id):
   
    if not notification_id:
        logger.warning("[_get_base_notification] Notification ID is missing, cannot fetch base notification.")
        return None
    try:
        base_notification = await sync_to_async(Notification.objects.get)(id=notification_id)
    except Notification.DoesNotExist:
        logger.warning(f"Base notification with ID {notification_id} does not exist.")
        return None
    except Exception as e:
        logger.error(f"Error fetching base notification with ID {notification_id}: {e}", exc_info=True)
        return None
    
    return base_notification

async def _create_user_notification(entry):
    """Create a UserNotification record for a successful notification."""

    notification = await _get_base_notification(entry['notification_id'])
    if not notification:
        logger.warning(f"Notification with ID {entry['notification_id']} not found, skipping UserNotification creation for user {entry['user_id']}.")
        return

    await sync_to_async(UserNotification.objects.create)(
        user_id=entry['user_id'],
        title=notification.title,
        body=entry['message'],
        fcm_message_id=entry['message_id'],
        notification=notification
    )

async def _process_successful_notifications(successful, window_name):
    """Process successful notifications and create UserNotification records."""
    if not successful:
        return

    for entry in successful:
        await _create_user_notification(entry)
        logger.info(f"[{window_name}] Notifications created for {len(successful)} users.")

async def _process_failed_notifications(failed, window_name):
    """Log failed notifications."""
    for entry in failed:
        logger.warning(
            f"[{window_name}] Failed to send to user {entry['user_id']} token {entry['token']}: {entry['error']}"
        )

async def _send_notifications_to_users(user_messages, window_name):
    """Send batched notifications to users and handle results."""
    # Prepare data for FCM
    notification_batches = {}

    for user_msg in user_messages:
        notification_id = user_msg.get('notification_id')
        notification_batches.setdefault(notification_id, []).append(user_msg)

    logger.info(f"[{window_name}] *******************Prepared {len(notification_batches)} notification batches.")
    successful = []
    failed = []

    # Send notifications in batch per notification type
    for notification_id, batch_user_messages in notification_batches.items():
        base_notification = await _get_base_notification(notification_id)

        res_success, res_fail = await fcm_send_with_retry(
            user_messages=batch_user_messages,
            base_notification=base_notification
        )

        successful.extend(res_success)
        failed.extend(res_fail)

    logger.info(f"[{window_name}] - {len(successful)} notifications sent successfully, {len(failed)} failed.")

    logger.info(f"[{window_name}] - {len(successful)} notifications sent successfully, {len(failed)} failed.")
    return successful, failed


async def handle_sqs_message(queue_name, msg, window_name):
    """Process a single SQS message."""
    logger.info(f"[{window_name}] Handling message from {queue_name}")

    try:
        body = json.loads(msg['Body'])
        user_messages = body.get("user_messages", [])

        if not user_messages:
            logger.info(f"[{window_name}] No user messages to process")
            return

        logger.info(f"[{window_name}] Sending notification to {len(user_messages)} users from {queue_name}")

        # Send notifications and get results
        successful, failed = await _send_notifications_to_users(
            user_messages, 
            window_name
        )

        # Process results
        await _process_successful_notifications(successful, window_name)
        await _process_failed_notifications(failed, window_name)

    except Exception as send_err:
        logger.error(f"[{window_name}] Notification send error: {send_err}", exc_info=True)
    finally:
        # Always attempt to delete message after processing
        delete_sqs_message(queue_name, msg['ReceiptHandle'])
        logger.info(f"[{window_name}] Messages processed and deleted.")

async def process_single_queue(queue_name: str, window_name: str):
    """Consume messages from a single SQS queue."""
    messages = receive_sqs_messages(queue_name)
    if not messages:
        logger.info(f"[{window_name}] No messages in {queue_name}")
        return

    for msg in messages:
        try:
            await handle_sqs_message(queue_name, msg, window_name)
        except Exception as e:
            logger.error(f"[{window_name}] Error processing message for [{queue_name}]: {e}", exc_info=True)

# Utility function for monthly_engagement
def update_last_month_nps(user_ids, window_name, tz_name):
    profiles = UserProfile.objects.filter(user_id__in=user_ids)

    for profile in profiles:
        profile.last_month_nps = profile.points

    UserProfile.objects.bulk_update(profiles, ['last_month_nps'])

    logger.info(f"[{window_name}] Updated last_month_nps for users in {tz_name}")

# Utility function for evening_wrap_ups
def update_last_daily_nps(user_ids, window_name, tz_name):
    # Update last_daily_nps for users
    for user in user_ids:
        profile = UserProfile.objects.get(user_id=user)
        profile.last_daily_nps = profile.points
        profile.save()
    logger.info(f"[{window_name}] Updated last_daily_nps for users in {tz_name}")

def format_evening_notif_for_user(profile, local_date_today, message, notification):

    base_notification = notification
    # Check if the user qualifies for GREAT_JOB
    todays_goals = SelectedGoal.objects.filter(
        user=profile,
        last_tracking_date=local_date_today,
    ).count()

    if todays_goals > 0:
        daily_nps_delta = (profile.points or 0) - (profile.last_daily_nps or 0)
        message = message.replace('[X]', str(todays_goals)).replace('[Y]', str(daily_nps_delta))
    else:
        # Select any other subtype for fallback
        fallback = Notification.objects.filter(
            type=NotificationType.EVENING_WRAP_UPS
        ).exclude(sub_type=NotificationSubType.GREAT_JOB).values(
            'title', 'message', 'type', 'sub_type', 'icon', 'id'
        )

        if fallback:
            base_notification = random.choice(list(fallback))
            message = base_notification['message']
    return message, base_notification