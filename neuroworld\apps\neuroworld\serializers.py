import json
from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer

from rest_framework import serializers
from pytz import all_timezones

from .s3_utils import generate_presigned_view_url
from .models import Badge, GoalTracking, GoalVersion, NeuroChat, Notification, PlanFeature, SubscriptionPlan, User, UserBadge, UserDevice, UserNotification, UserProfile, Goal, SelectedGoal, UserSubscription
import uuid
from .constants import GOAL_RULES

class TimeZoneValidationMixin:
    def validate_time_zone(self, value):
        if not value:
            raise serializers.ValidationError("This field is required.")
        if value not in all_timezones:
            raise serializers.ValidationError("Invalid time_zone.")
        return value

    def validate(self, data):
        timezone_str = data.get('time_zone')
        self.validate_time_zone(timezone_str)
        # Store in context to access it later in the view
        self.context['time_zone'] = timezone_str
        return super().validate(data)

class UserSerializer(TimeZoneValidationMixin, serializers.ModelSerializer):
    time_zone = serializers.CharField(write_only=True, required=True)
    fcm_token = serializers.CharField(required=False, allow_blank=True, allow_null=True, write_only=True)
    has_active_subscription = serializers.SerializerMethodField()
    active_subscription = serializers.SerializerMethodField()
    badges = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = (
            'id', 'email', 'password', 'time_zone', 'fcm_token', 
            'has_active_subscription', 'active_subscription', 'badges'
        )
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        user = User.objects.create_user(
            email=validated_data['email'],
            password=validated_data['password']
        )
        return user

    def get_has_active_subscription(self, obj):
        return obj.has_active_subscription

    def get_active_subscription(self, obj):
        subscription = obj.active_subscription
        if not subscription:
            return None
        
        subscription_data = UserSubscriptionSerializer(subscription).data
        # Add the full plan data to the subscription
        subscription_data['plan'] = SubscriptionPlanSerializer(subscription.plan).data if subscription.plan else None
        return subscription_data

    def get_badges(self, obj):
        user_badges = UserBadge.objects.filter(user=obj)
        return UserBadgeSerializer(user_badges, many=True).data

class TokenSerializer(TimeZoneValidationMixin, serializers.Serializer):
    id_token = serializers.CharField()
    time_zone = serializers.CharField(required=False)
    fcm_token = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    class Meta:
        fields = ['id_token', 'time_zone', 'fcm_token']

class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for the UserProfile model.

    This serializer is used to transform UserProfile model instances into JSON representations and vice versa.

    Meta:
        model (UserProfile): The model that this serializer is based on.
        fields (list): All fields of the UserProfile model should be included in the serialized representation.
    """
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    profile_picture_url = serializers.SerializerMethodField()

    def get_profile_picture_url(self, obj):
        if obj.profile_image_path:
            url = generate_presigned_view_url(obj.profile_image_path)
            if not url:
                raise serializers.ValidationError("Failed to generate pre-signed S3 URL.")
            return url
        return None
    
    class Meta:
        model = UserProfile
        exclude = ['profile_image_path']  # Exclude internal key
        extra_kwargs = {
            'id': {'required': False},
            'points': {'required': False},
            'is_onboarding_completed': {'required': False}
        }


class NeuroChatSerializer(serializers.ModelSerializer):

    class Meta:
        model = NeuroChat
        fields = '__all__'


class ChatStreamSerializer(serializers.Serializer):
    chunk = NeuroChatSerializer()
    last_message = serializers.BooleanField()

class UpdatePasswordSerializer(serializers.Serializer):
    new_password = serializers.CharField(write_only=True, required=True)
    token = serializers.CharField(write_only=True, required=True)

class EmailSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)

class GoalVersionSerializer(serializers.ModelSerializer):
    class Meta:
        model = GoalVersion
        fields = '__all__'

class GoalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Goal
        fields = '__all__'

class SelectedGoalSerializer(serializers.ModelSerializer):
    rule_text = serializers.SerializerMethodField()
    class Meta:
        model = SelectedGoal
        fields = '__all__'

    def validate(self, data):
        # Only enforce required fields on creation (POST request)
        if self.instance is None:  # self.instance exists in update but not in create
            required_fields = ["goal_version", "start_date", "month_target", "week_target"]
            missing_fields = [field for field in required_fields if field not in data]

            if missing_fields:
                raise serializers.ValidationError({field: "This field is required." for field in missing_fields})

        return data
    
    def get_rule_text(self, instance):
        try:
            goal_name = instance.goal_version.goal.title
        except AttributeError:
            raise serializers.ValidationError("Goal version or goal title is not available.")

        # Use instance.target or 0 if None
        user_target = instance.target if instance.target else 0

        # Determine singular/plural day(s)
        day_label = "day" if user_target == 1 else "days"

        # Format your GOAL_RULES string accordingly
        return GOAL_RULES.format(goal_name=goal_name.lower(), target_days=f"{user_target} {day_label}")


    def to_representation(self, instance):
        representation = super().to_representation(instance)
        for field, value in representation.items():
            if isinstance(value, uuid.UUID):
                representation[field] = str(value)
        return representation
        
    
class GoalTrackingSerializer(serializers.ModelSerializer):
    class Meta:
        model = GoalTracking
        fields = '__all__'

class ProfilePictureUploadSerializer(serializers.ModelSerializer):
    profile_picture = serializers.ImageField()

    def validate_profile_picture(self, file):
        if file.size > 2 * 1024 * 1024:  # 2MB max
            raise serializers.ValidationError("Image size should not exceed 2MB.")
        
        if file.content_type not in ['image/jpeg', 'image/png']:
            raise serializers.ValidationError("Only JPEG and PNG formats are supported.")
        
        return file
    class Meta:
        model = UserProfile
        fields = ['profile_picture']


class FeedbackEmailSerializer(serializers.Serializer):
    comment = serializers.CharField(required=False, allow_blank=True)
    score = serializers.IntegerField()

    def validate_score(self, value):
        if not (0 <= value <= 10):
            raise serializers.ValidationError("Score must be an integer between 0 and 10.")
        return value


class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = ['type', 'sub_type', 'icon']  # Excluding title and message

class UserNotificationSerializer(serializers.ModelSerializer):
    notification = NotificationSerializer()
    class Meta:
        model = UserNotification
        fields = ['id', 'title', 'body', 'timestamp', 'notification']

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Custom serializer for obtaining JWT tokens with additional user information.
    """
    fcm_token = serializers.CharField(required=False, allow_blank=True, allow_null=True)


class UserDeviceSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserDevice
        exclude = ["updated_at", "user"]  # Exclude updated_at field from the serialized output

class CustomNeuroChatSerializer(NeuroChatSerializer):
    def to_representation(self, instance):
        data = super().to_representation(instance)
        
        # For assistant messages (already in JSON string format)
        if instance.role == 'assistant':
            try:
                data['message'] = json.loads(data['message'])
            except json.JSONDecodeError:
                # If parsing fails, wrap the message in the standard format
                data['message'] = {
                    "text": data['message'],
                    "avatar": None,
                    "actionType": "None",
                    "actions": []
                }
        
        # For user messages (plain text)
        elif instance.role == 'user':
            data['message'] = {
                "text": data['message'],
                "avatar": None,  # or "User" if you want to specify
                "actionType": "None",
                "actions": []
            }
            
        return data
    

class PlanFeatureSerializer(serializers.ModelSerializer):
    class Meta:
        model = PlanFeature
        fields = ['description', 'icon']


class SubscriptionTierSerializer(serializers.Serializer):
    tier = serializers.CharField()
    features = PlanFeatureSerializer(many=True)
    yearly_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True)
    monthly_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True)
    quarterly_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True)
    colors = serializers.ListField(child=serializers.CharField())

class UserSubscriptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserSubscription
        fields = ['id', 'plan', 'status', 'start_time', 'expiry_time']


class SubscriptionPlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubscriptionPlan
        fields = ['id', 'tier', 'name', 'billing_period', 'product_id', 'duration_days']

class BadgeSerializer(serializers.ModelSerializer):
    is_earned = serializers.SerializerMethodField()
    earned_at = serializers.SerializerMethodField()
    
    class Meta:
        model = Badge
        fields = ['id', 'title', 'description', 'image_url', 'badge_type', 
                 'level', 'category', 'city_area', 'is_earned', 'earned_at']
    
    def get_is_earned(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return UserBadge.objects.filter(user=request.user, badge=obj).exists()
        return False
    
    def get_earned_at(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            user_badge = UserBadge.objects.filter(user=request.user, badge=obj).first()
            return user_badge.earned_at if user_badge else None
        return None

class UserBadgeSerializer(serializers.ModelSerializer):
    badge = BadgeSerializer()
    
    class Meta:
        model = UserBadge
        fields = ['id', 'badge', 'earned_at']