-i https://pypi.org/simple
aiohappyeyeballs==2.6.1; python_version >= '3.9'
aiohttp==3.11.18; python_version >= '3.9'
aiosignal==1.3.2; python_version >= '3.9'
amqp==5.3.1; python_version >= '3.6'
annotated-types==0.7.0; python_version >= '3.8'
anyio==4.8.0; python_version >= '3.9'
argon2-cffi==23.1.0; python_version >= '3.7'
argon2-cffi-bindings==21.2.0; python_version >= '3.6'
asgiref==3.8.1; python_version >= '3.8'
attrs==24.3.0; python_version >= '3.8'
billiard==4.2.1; python_version >= '3.7'
boto3==1.36.15; python_version >= '3.8'
botocore==1.36.15; python_version >= '3.8'
cachecontrol==0.14.2; python_version >= '3.8'
cachetools==5.5.1; python_version >= '3.7'
celery[sqs]==5.5.2; python_version >= '3.8'
certifi==2024.12.14; python_version >= '3.6'
cffi==1.17.1; python_version >= '3.8'
charset-normalizer==3.4.1; python_version >= '3.7'
cryptography==44.0.2; python_version >= '3.7' and python_full_version not in '3.9.0, 3.9.1'
distro==1.9.0; python_version >= '3.6'
dj-database-url==2.3.0
django==4.2.18; python_version >= '3.8'
django-celery-beat==2.8.0; python_version >= '3.8'
django-cors-headers==3.14.0; python_version >= '3.7'
django-environ==0.12.0; python_version >= '3.9' and python_version < '4'
django-filter==23.3; python_version >= '3.7'
django-model-utils==4.5.1; python_version >= '3.8'
django-storages==1.14.6; python_version >= '3.7'
django-timezone-field==7.1; python_version >= '3.8' and python_version < '4.0'
djangorestframework==3.15.2; python_version >= '3.8'
djangorestframework-simplejwt==5.4.0; python_version >= '3.9'
drf-spectacular==0.28.0; python_version >= '3.7'
drf-yasg==1.21.8; python_version >= '3.6'
firebase-admin==6.8.0; python_version >= '3.7'
google-api-core[grpc]==2.24.2; python_version >= '3.7'
google-api-python-client==2.169.0; python_version >= '3.7'
google-auth==2.37.0; python_version >= '3.7'
google-auth-httplib2==0.2.0
google-cloud-core==2.4.3; python_version >= '3.7'
google-cloud-firestore==2.20.2; python_version >= '3.7'
google-cloud-storage==3.1.0; python_version >= '3.7'
google-crc32c==1.7.1; python_version >= '3.9'
google-resumable-media==2.7.2; python_version >= '3.7'
googleapis-common-protos==1.70.0; python_version >= '3.7'
grpcio==1.71.0; python_version >= '3.9'
grpcio-status==1.71.0; python_version >= '3.9'
gunicorn==20.1.0; python_version >= '3.5'
h11==0.14.0; python_version >= '3.7'
httpcore==1.0.7; python_version >= '3.8'
httplib2==0.22.0; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3'
httpx==0.28.1; python_version >= '3.8'
idna==3.10; python_version >= '3.6'
inflection==0.5.1; python_version >= '3.5'
jiter==0.8.2; python_version >= '3.8'
jmespath==1.0.1; python_version >= '3.7'
jsonschema==4.23.0; python_version >= '3.8'
jsonschema-specifications==2024.10.1; python_version >= '3.9'
msgpack==1.1.0; python_version >= '3.8'
openai==1.59.9; python_version >= '3.8'
packaging==24.2; python_version >= '3.8'
pillow==11.2.1; python_version >= '3.9'
proto-plus==1.26.1; python_version >= '3.7'
protobuf==5.29.4; python_version >= '3.8'
psycopg2-binary==2.9.10; python_version >= '3.8'
pyasn1==0.6.1; python_version >= '3.8'
pyasn1-modules==0.4.1; python_version >= '3.8'
pycparser==2.22; python_version >= '3.8'
pydantic==2.10.5; python_version >= '3.8'
pydantic-core==2.27.2; python_version >= '3.8'
pyfcm==2.0.8
pyjwt[crypto]==2.10.1; python_version >= '3.9'
pyparsing==3.2.3; python_version >= '3.9'
python-dateutil==2.9.0.post0; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3'
pytz==2022.7.1
pyyaml==6.0.2; python_version >= '3.8'
referencing==0.36.1; python_version >= '3.9'
requests==2.32.3; python_version >= '3.8'
rpds-py==0.22.3; python_version >= '3.9'
rsa==4.9; python_version >= '3.6' and python_version < '4'
s3transfer==0.11.2; python_version >= '3.8'
setuptools==79.0.0; python_version >= '3.9'
six==1.17.0; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3'
sniffio==1.3.1; python_version >= '3.7'
sqlparse==0.5.3; python_version >= '3.8'
tqdm==4.67.1; python_version >= '3.7'
typing-extensions==4.12.2; python_version >= '3.8'
tzdata==2025.2; python_version >= '2'
uritemplate==4.1.1; python_version >= '3.6'
urllib3==2.3.0; python_version >= '3.9'
vine==5.1.0; python_version >= '3.6'
wcwidth==0.2.13
whitenoise==6.8.2; python_version >= '3.9'
yarl==1.20.0; python_version >= '3.9'
python-dotenv
huggingface_hub
python-multipart
langchain
langchain_openai
langchain_pinecone
langchain-community
langchain_huggingface
weaviate-client==3.25.3
langgraph
wagtail
pyPDF2
pymupdf
python-docx
posthog==5.4.0