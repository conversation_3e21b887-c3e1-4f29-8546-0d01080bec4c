from langgraph.graph import <PERSON><PERSON>
from .state import State

def select_category_node_workflow_selector(state: State):
  if 'select_category_node' not in state['executed_nodes']:
    return 'select_category_node'
  if 'goals_options_node' not in state['executed_nodes']:
    return 'goals_options_node'
  if 'character_switch_node' not in state['executed_nodes']:
    return 'character_switch_node'
  if 'days_selection_node' not in state['executed_nodes']:
    return 'days_selection_node'
  if 'confirmation_question_node' not in state['executed_nodes']:
    return 'confirmation_question_node'
  else:
    return END
