ARG ECR_REPOSITORY_URI=${ECR_REPOSITORY_URI}
ARG TAG=${TAG}

FROM ${ECR_REPOSITORY_URI}:${TAG}

WORKDIR /app

COPY . /app
COPY cicd/scripts/entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

RUN pip install --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

EXPOSE 80

ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["gunicorn", "--bind", "0.0.0.0:80", "--timeout", "300", "neuroworld.wsgi"]
