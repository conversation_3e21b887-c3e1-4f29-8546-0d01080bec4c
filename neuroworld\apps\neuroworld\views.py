# Create your views here.
import asyncio
import json
import json
import re
from django.utils import timezone
from datetime import datetime
from typing import List
from django.conf import settings
from django.http import JsonResponse, StreamingHttpResponse
from django.shortcuts import get_object_or_404
from rest_framework import viewsets, filters
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAdminUser
from rest_framework.pagination import PageNumberPagination
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import ValidationError
from rest_framework.views import APIView
from drf_yasg import openapi
import base64

from google.oauth2 import id_token, service_account
from google.auth.transport import requests

import requests as requests_lib

from .payment_processor import PurchaseProcessor, GooglePurchaseProcessor

from apps.neuroworld.services.badge_service import BadgeService

from .s3_utils import S3OperationError, delete_s3_object, upload_s3_file

from .utils import decode_signed_data, edit_goal, generate_token, get_user_from_transaction, register_fcm_token, restore_streak, send_email, send_goal_setting_encouragement_notification, send_welcome_notification, track_goal, get_host_url, verify_apple_notification
from django.template.loader import render_to_string
from .assistant import Assistant
from .models import  Feedback, Goal, GoalTracking, GoalVersion, NeuroChat, PlanFeature, SubscriptionPlan, User, UserBadge, UserDevice, UserNotification, UserProfile, UserSubscription, UserThread, SelectedGoal, PlanTier
# PostHog Analytics imports - temporarily commented out for startup
# from .analytics import analytics
# from .analytics_queries import analytics_queries
from .serializers import BadgeSerializer, ChatStreamSerializer, CustomNeuroChatSerializer, CustomTokenObtainPairSerializer, FeedbackEmailSerializer, GoalSerializer, GoalTrackingSerializer, GoalVersionSerializer, NeuroChatSerializer, PlanFeatureSerializer, ProfilePictureUploadSerializer, SubscriptionTierSerializer, UserBadgeSerializer, UserDeviceSerializer, UserNotificationSerializer, UserProfileSerializer, UserSerializer, TokenSerializer, SelectedGoalSerializer, UpdatePasswordSerializer, EmailSerializer
from .interactors import chat as chat_interactor
from .services.weaviate_db import WeaviateDB
from drf_yasg.utils import swagger_auto_schema
import jwt
from django_filters.rest_framework import DjangoFilterBackend
from django.db import IntegrityError
from openai import APIConnectionError, APIError, RateLimitError
import logging

from rest_framework.decorators import api_view
from rest_framework.decorators import permission_classes

from django.utils.http import urlsafe_base64_decode
from drf_yasg import openapi
from django.contrib.auth.tokens import default_token_generator 
from rest_framework.decorators import action
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.generics import ListAPIView

logger = logging.getLogger(__name__)

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        try:
            # Call the parent class's post method to validate credentials
            user = self.get_user(request.data.get('email'))

            if not user:
                return Response(
                    {"detail": "User not found."},
                    status=status.HTTP_400_BAD_REQUEST
                )
                        
            if user and user.auth_provider != "BASE":
                return Response(
                    {"detail": f"This account was created using {user.auth_provider.lower()}. Please use {user.auth_provider.lower()} to log in."},
                    status=status.HTTP_400_BAD_REQUEST
                )
                        
            response = super().post(request, *args, **kwargs)

            user_profile = UserProfile.objects.get(user=user)
            user_profile.save()

            # Track user login in PostHog - temporarily commented out
            # analytics.identify_user(user)
            # analytics.track_user_login(user, auth_provider="BASE")

            fcm_token = request.data.get('fcm_token')

            if fcm_token:
                register_fcm_token(user, fcm_token)

            send_welcome_notification(user) # One time welcome notification to the user

            selected_goals = SelectedGoal.objects.filter(user=user_profile)
            selected_goals_data = [
                {
                    **SelectedGoalSerializer(goal).data,
                    "goal": GoalSerializer(goal.goal_version.goal).data
                }
                for goal in selected_goals
            ]

            response.data.update({
                'user': {**UserSerializer(user).data, 'profile': UserProfileSerializer(user_profile).data, "selected_goals": selected_goals_data}
            })
            
            return response
        
        except User.DoesNotExist:
            # Return a custom 400 response if the user does not exist
            return Response(
                {"detail": "Invalid email or password."},
                status=status.HTTP_400_BAD_REQUEST
            )
        except AuthenticationFailed:
            # Handle authentication failure and return a custom 400 response
            return Response(
                {"detail": "Invalid email or password."},
                status=status.HTTP_400_BAD_REQUEST
            )
        except ValidationError as e:
            # Handle validation errors and return a custom 400 response
            return Response(
                {"detail": e.detail},
                status=status.HTTP_400_BAD_REQUEST
            )
        except ValueError as e:
            # Handle value errors and return a custom 400 response
            return Response(
                {"detail": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except IntegrityError as e:
            # Handle integrity errors and return a custom 400 response
            return Response(
                {"detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            # Handle any other exceptions and return a custom 400 response
            return Response(
                {"detail": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


    def get_user(self, email):
        try:
            return User.objects.get(email=email)
        except User.DoesNotExist:
            return None  

@swagger_auto_schema(operation_id="retrieveMe", responses={200: UserSerializer()})
class UserViewSet(viewsets.ModelViewSet):
    """
    Viewset for managing User objects.

    This viewset provides CRUD operations for User objects. It uses the UserSerializer for serialization
    and supports filtering by 'id', 'username' and 'auth_provider'.

    Methods:
        create(self, request, *args, **kwargs): Create a new User instance. Overrides the base method of viewsets.ModelViewSet class
    """

    queryset = User.objects.all()
    serializer_class = UserSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["id", "auth_provider"]

    def perform_authentication(self, request):
        if 'HTTP_AUTHORIZATION' in request.META:
            request.META.pop('HTTP_AUTHORIZATION')
  
    def create(self, request, *args, **kwargs):
        
        """
        Create a new User instance.

        This method is used to create a new User instance from the provided request data. It validates
        the data using the UserSerializer and performs the creation operation.

        Args:
            request: The HTTP request object.
            *args: Additional positional arguments.
            **kwargs: Additional keyword arguments.

        Returns:
            Response: A response indicating the result of the creation operation.
        """

        user_email = request.data.get('email', None)
        if user_email is None:
            return Response(status=400)
        
        user = User.objects.filter(email=user_email)
        
        if user.exists():
            user_profile = UserProfile.objects.filter(user=user.first()).first()
            selected_goals = SelectedGoal.objects.filter(user=user_profile)
            selected_goals_data = SelectedGoalSerializer(selected_goals, many=True).data
            selected_goals_data = [
                {
                        **SelectedGoalSerializer(goal).data,
                        "goal": GoalSerializer(goal.goal_version.goal).data
                    }
                for goal in selected_goals
            ]
            token = RefreshToken.for_user(user.first())
            response = {'detail': 'User already exist', 'access': str(token.access_token), 'refresh': str(token), 'user': {**UserSerializer(user.first()).data, 'profile': UserProfileSerializer(user_profile).data, "selected_goals": selected_goals_data}}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)

        # Create UserProfile instance
        user_instance = User.objects.get(id=serializer.data['id'])
        full_name = request.data.get('full_name', None)
        time_zone= request.data.get('time_zone')
        fcm_token = request.data.get('fcm_token')

        try:
            user_profile=UserProfile.objects.create(user=user_instance, full_name=full_name, time_zone=time_zone)
            token = RefreshToken.for_user(user_instance)

            # Track user registration in PostHog - temporarily commented out
            # analytics.identify_user(user_instance)
            # analytics.track_user_registration(user_instance)

            if fcm_token:
                UserDevice.objects.create(user=user_instance, fcm_token=fcm_token)

            logger.info(f"User {user_instance.email} created successfully.")

            selected_goals = SelectedGoal.objects.filter(user=user_profile)
            selected_goals_data = SelectedGoalSerializer(selected_goals, many=True).data
            selected_goals_data = [
                {
                        **SelectedGoalSerializer(goal).data,
                        "goal": GoalSerializer(goal.goal_version.goal).data
                    }
                for goal in selected_goals
            ]            
        except IntegrityError:
            logger.error(f"Error creating UserProfile for user: {user_instance.id}")
            return Response({"detail": "Error creating UserProfile"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except ValueError as err:
            logger.error(f"Value error: {str(err)}")
            return Response({"detail": str(err)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as err:
            logger.error(f"Unexpected error: {str(err)}")
            return Response({"detail": str(err)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        response = {'detail': 'User created successfully','access': str(token.access_token), 'refresh': str(token), 'user': {**serializer.data, 'profile': UserProfileSerializer(user_profile).data, "selected_goals": selected_goals_data}}
        return Response(response, status=status.HTTP_201_CREATED, headers=headers)

def get_or_create_user(User, UserSerializer, idinfo, RefreshToken, auth_provider):
    email = idinfo.get('email')
    user, created = User.objects.get_or_create(email=email, defaults={'auth_provider': auth_provider})
    if created:
        user.set_unusable_password()
        user.save()
    token = RefreshToken.for_user(user)
    serializer = UserSerializer(user)
    return token, serializer, user

class GoogleView(viewsets.ViewSet):
    permission_classes = [AllowAny]
    serializer_class = TokenSerializer

    @swagger_auto_schema(request_body=TokenSerializer)
    def post(self, request):
        token = {'id_token': request.data.get('id_token')}
        time_zone = request.data.get('time_zone')
        try:
            idinfo = id_token.verify_oauth2_token(token['id_token'], requests.Request(), None)
            if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                raise ValueError('Wrong issuer.')
            token, serializer, user = get_or_create_user(User=User, UserSerializer=UserSerializer, idinfo=idinfo, RefreshToken=RefreshToken, auth_provider="GOOGLE")
            full_name = f"{idinfo.get('given_name')} {idinfo.get('family_name')}"
            user_profile, created = UserProfile.objects.get_or_create(user=user, defaults={"full_name": full_name, "time_zone": time_zone})

            # Track user in PostHog - temporarily commented out
            # analytics.identify_user(user)
            # if created:
            #     analytics.track_user_registration(user)
            # analytics.track_user_login(user, auth_provider="GOOGLE")
            selected_goals = SelectedGoal.objects.filter(user=user_profile)
            selected_goals_data = SelectedGoalSerializer(selected_goals, many=True).data
            selected_goals_data = [
                {
                        **SelectedGoalSerializer(goal).data,
                        "goal": GoalSerializer(goal.goal_version.goal).data
                    }
                for goal in selected_goals
            ]  
            
            fcm_token = request.data.get('fcm_token')

            if fcm_token:
                register_fcm_token(user, fcm_token)

            # Send welcome notification to the user
            send_welcome_notification(user)

            # construct response with access token, refresh token and (user with userprofile and with user's selected goals)
            response = {'access': str(token.access_token), 
                        'refresh': str(token), 
                        'user': {
                            **serializer.data, 
                            'profile': UserProfileSerializer(user_profile).data, 
                            "selected_goals": selected_goals_data
                        }}


            return Response(response, status=status.HTTP_200_OK)
        except ValueError as err:
            logger.error(f"Value error while processing Google login: {str(err)}")
            content = {'message': str(err)}
            return Response(content, status=status.HTTP_400_BAD_REQUEST)
        except IntegrityError as err:
            logger.error(f"Integrity error while processing Google login: {str(err)}")
            content = {'message': str(err)}
            return Response(content, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except Exception as err:
            logger.error(f"Unexpected error while processing Google login: {str(err)}")
            content = {'message': str(err)}
            return Response(content, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class AppleView(viewsets.ViewSet):
    permission_classes = [AllowAny]
    serializer_class = TokenSerializer

    @swagger_auto_schema(request_body=TokenSerializer)
    def post(self, request):
        id_token = request.data.get('id_token', None)
        decoded = jwt.decode(id_token, verify=False, options={"verify_signature": False}) if id_token else None
        time_zone = request.data.get('time_zone')

        if id_token and decoded:
            try:
                token, serializer, user = get_or_create_user(User=User, UserSerializer=UserSerializer, idinfo=decoded, RefreshToken=RefreshToken, auth_provider="APPLE")
                full_name = decoded.get('name', None)
                user_profile, created = UserProfile.objects.get_or_create(user=user, defaults={"full_name": full_name, "time_zone": time_zone})

                # Track user in PostHog - temporarily commented out
                # analytics.identify_user(user)
                # if created:
                #     analytics.track_user_registration(user)
                # analytics.track_user_login(user, auth_provider="APPLE")
                selected_goals = SelectedGoal.objects.filter(user=user_profile)
                selected_goals_data = SelectedGoalSerializer(selected_goals, many=True).data
                selected_goals_data = [
                    {
                            **SelectedGoalSerializer(goal).data,
                            "goal": GoalSerializer(goal.goal_version.goal).data
                        }
                    for goal in selected_goals
                ]

                fcm_token = request.data.get('fcm_token')

                if fcm_token:
                    register_fcm_token(user, fcm_token)

                # Send welcome notification to the user
                send_welcome_notification(user)

                data = {'access': str(token.access_token), 'refresh': str(token), 'user': {**serializer.data, 'profile': UserProfileSerializer(user_profile).data, "selected_goals": selected_goals_data}}
                return Response(data, status=status.HTTP_200_OK)
            except AssertionError as err:
                logger.error(f"Assertion error while processing Apple login: {str(err)}")
                content = {'message': str(err)}
                return Response(content, status=status.HTTP_400_BAD_REQUEST)
            except IntegrityError as err:
                logger.error(f"Integrity error while processing Apple login: {str(err)}")
                content = {'message': str(err)}
                return Response(content, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            except ValueError as err:
                logger.error(f"Value error while processing Apple login: {str(err)}")
                content = {'message': str(err)}
                return Response(content, status=status.HTTP_400_BAD_REQUEST)
            except Exception as err:
                logger.error(f"Unexpected error while processing Apple login: {str(err)}")
                content = {'message': str(err)}
                return Response(content, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({'message': 'Invalid token'}, status=status.HTTP_400_BAD_REQUEST)

@swagger_auto_schema(request_body=UserProfileSerializer, responses={200: UserProfileSerializer()})    
class UserProfileViewSet(viewsets.ModelViewSet):
    """
    Viewset for managing UserProfile objects.

    This viewset provides CRUD operations for UserProfile objects. It uses the UserProfileSerializer for serialization
    and supports filtering by 'id' and 'user'.

    Permission Classes:
        - IsAuthenticated: Only authenticated users are allowed to access this viewset.
    """

    permission_classes = [IsAuthenticated]
    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["id", "user"]

    def get_queryset(self):
        user = self.request.user
        return UserProfile.objects.filter(user=user)

    def destroy(self, request, *args, **kwargs):
        try:
            user_profile = self.get_object()
            user = user_profile.user
            
            logger.info(f"Attempting to delete UserProfile with ID: {user_profile.id} for user: {user.email}")
            user.delete()
            logger.info(f"UserProfile with ID: {user_profile.id} and User {user.email} deleted successfully.")
            
            return Response({"detail": "User deleted successfully."}, status=status.HTTP_204_NO_CONTENT)
        
        except UserProfile.DoesNotExist:
            logger.error(f"UserProfile with ID: {kwargs['pk']} not found.")
            return Response({"detail": "UserProfile not found."}, status=status.HTTP_404_NOT_FOUND)
        
    def partial_update(self, request, *args, **kwargs):
        try:
            instance = self.get_object()

            # Track which fields are being updated
            updated_fields = list(request.data.keys())

            serializer = self.get_serializer(instance, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)

            user_profile = UserProfile.objects.filter(user=request.user).only(
                'full_name', 'birth_date', 'gender'
            ).first()

            if not user_profile:
                logger.error("UserProfile not found for the user")
                return Response({"detail": "UserProfile not found."}, status=status.HTTP_404_NOT_FOUND)

            is_complete = all([
                user_profile.full_name,
                user_profile.birth_date,
                user_profile.gender
            ])

            # Track profile completion if it just became complete
            was_complete = user_profile.is_profile_completed
            user_profile.is_profile_completed = is_complete
            user_profile.profile_completion_reminder_date = None if is_complete else timezone.now() + timezone.timedelta(days=3)

            user_profile.save(update_fields=['is_profile_completed', 'profile_completion_reminder_date'])

            # Track profile update in PostHog - temporarily commented out
            # analytics.track_profile_update(request.user, updated_fields)
            # analytics.identify_user(request.user)  # Update user properties

            # Track profile completion if it just became complete - temporarily commented out
            # if is_complete and not was_complete:
            #     analytics.track_event(
            #         user=request.user,
            #         event_name='Profile Completed',
            #         properties={}
            #     )

            return Response(UserProfileSerializer(user_profile).data, status=status.HTTP_200_OK)
        except UserProfile.DoesNotExist:
            return Response({"detail": "UserProfile not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"An error occurred during partial update: {str(e)}", exc_info=True)
            return Response({"detail": "An unexpected error occurred."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

    def paginate_queryset(self, queryset, request, view=None):
        page = super().paginate_queryset(queryset, request, view)
        if page is not None:
            page.reverse()
        return page


class NeuroChatViewSet(viewsets.ModelViewSet):
    queryset = NeuroChat.objects.all()
    serializer_class = CustomNeuroChatSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['id', 'user', 'role']
    
    def get_queryset(self):
        try:
            chats = NeuroChat.objects.filter(user=self.request.user).order_by('-date').values('message')
        except Exception as e:
            logger.error(f"Error in getting chat: {e}")    
        return NeuroChat.objects.filter(user=self.request.user).order_by('-date')
    
    def create(self, request):
        user = request.user
        user_message = request.data.get('message')
        new_thread = request.data.get('new_thread', False)
        if not user_message:
            return Response({'message': "Message not provided"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            assistant = Assistant(user)
            if new_thread:
                thread = assistant.get_or_create_thread(new_thread)
                thread = thread.id
            else:
                thread = user.thread_id    
            assistant.create_message(thread.id, user_message)
            assistant.run(thread.id)
            reply_message = assistant.get_messages(thread_id=thread.id, limit=1)[0][1]
            
            NeuroChat.objects.create(
                user=user,
                message=user_message,
                role="user",
            )
            reply = NeuroChat.objects.create(
                user=user,
                message=reply_message,
                role="assistant",
            )
            user_thread, created = UserThread.objects.get_or_create(user=user, thread_id=thread.id)

            if created:
                user_thread.message_count = 2
                user_thread.save()
            else:
                user_thread.message_count =+ 2
                user_thread.save()

            serializer = NeuroChatSerializer(reply)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except (APIError, APIConnectionError, RateLimitError) as e:
            logger.error(f"Error in creating chat: {e}")
            return Response({'message': "Error in creating chat"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error in creating chat: {e}")
            return Response({'message': "Error in creating chat"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@swagger_auto_schema(
    method='post',
    request_body=NeuroChatSerializer,
    responses={200: ChatStreamSerializer()}
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def stream_chat(request):
    """
    Stream chat messages.

    This view handles streaming chat messages for authenticated users. It accepts a message and an optional
    new_thread flag to create a new thread. The assistant processes the message and streams the response.

    Args:
        request: The HTTP request object containing the message and new_thread flag.

    Returns:
        StreamingHttpResponse: A streaming response with chat messages.
    """
    user = request.user
    message = request.data.get('message')
    new_thread = request.data.get('new_thread', False)

    if not message:
        return Response({'message': "Message not provided"}, status=status.HTTP_400_BAD_REQUEST)

    try:
        assistant = Assistant(user, message=message)

        user_profile = UserProfile.objects.filter(user=user).first()
        
        if user_profile.is_chat_reset:
            new_thread = True
            user_profile.is_chat_reset = False
            user_profile.save()
        
        if new_thread or not user.thread_id:
            thread = assistant.get_or_create_thread(new=new_thread)
            thread = thread.id
        else:
            thread = user.thread_id
        
        assistant.create_message(thread, message)     
        
        def event_stream():
            for event in assistant.get_thread_stream_async(thread_id=thread):
                yield f"data: {event}\n\n"
        
        response = StreamingHttpResponse(
            event_stream(),
            status=status.HTTP_200_OK,
            content_type="text/event-stream"  
        )
        response['Cache-Control'] = 'no-cache'  
        response['X-Accel-Buffering'] = 'no'  
        return response

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"Error in creating chat: {e}")
        return Response({'message': "Error in creating chat"}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error streaming chat: {e}")
        return Response({'message': "Error streaming chat"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
class ForgotPasswordView(viewsets.ViewSet):
    """Handles forgot password requests and sends a reset link via email."""

    @swagger_auto_schema(request_body=EmailSerializer, operation_id="forgot-password")
    def post(self, request):
        serializer = EmailSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        email = serializer.validated_data.get("email")
        user = User.objects.filter(email=email).first()
        if not user:
            return Response({'message': 'User not found.'}, status=status.HTTP_404_NOT_FOUND)

        # Generate reset token and fetch user details
        token = generate_token(user)
        user_name = UserProfile.objects.filter(user=user).values_list("full_name", flat=True).first() or "User"

        # Send reset email
        self.send_reset_password_email(user_name, email, token)

        return Response({'message': 'Email sent successfully.'}, status=status.HTTP_200_OK)

    def send_reset_password_email(self, user_name, recipient_email, token):
        """Sends the password reset email to the user."""

        reset_link = f"{settings.RESET_PASSWORD_DEEP_LINK}/{token}" 
        html_content = render_to_string("reset-password.html", {"first_name": user_name, "link": reset_link})
        send_email(settings.EMAIL_HOST_USER, [recipient_email], "Password Reset", html_content, "Click the link to reset your password.")

class UpdatePasswordView(viewsets.ViewSet):
    """Handles password reset requests."""

    @swagger_auto_schema(request_body=UpdatePasswordSerializer, operation_id="reset-password")
    def post(self, request):
        serializer = UpdatePasswordSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        token = serializer.validated_data.get('token')[6:]
        uid, token = self.decode_token(token)
        
        user = User.objects.filter(pk=uid).first()
        if not user or not default_token_generator.check_token(user, token):
            return Response({'error': 'Invalid token or user not found'}, status=status.HTTP_400_BAD_REQUEST)
        
        user.set_password(serializer.validated_data.get('new_password'))
        user.save()

        return Response({'message': 'Password has been reset successfully'}, status=status.HTTP_200_OK)

    def decode_token(self, token):
        """Decodes the user ID from the token."""

        try:
            uid, token = token.split('-', 1)
            return urlsafe_base64_decode(uid).decode('utf-8'), token
        except (ValueError, TypeError):
            return Response({'error': 'Invalid token'}, status=status.HTTP_400_BAD_REQUEST)


class GoalVersionViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows GoalVersions to be viewed or edited.
    Includes filtering by goal_id and level.
    Prevents duplicate goal levels for the same goal.
    """
    queryset = GoalVersion.objects.all()
    serializer_class = GoalVersionSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['goal', 'level']

    def perform_create(self, serializer):
        goal = serializer.validated_data.get('goal')
        level = serializer.validated_data.get('level')

        # Check for duplicate goal levels
        if GoalVersion.objects.filter(goal=goal, level=level).exists():
            raise ValidationError(f"A GoalVersion with level '{level}' already exists for this goal.")

        serializer.save()


@swagger_auto_schema(
    manual_parameters=[
        openapi.Parameter(
            'all', openapi.IN_QUERY, description="Include all goals", type=openapi.TYPE_BOOLEAN, required=False
        ),
        openapi.Parameter(
            'category', openapi.IN_QUERY, description="Filter by category", type=openapi.TYPE_STRING, required=False
        )
    ],
    responses={
        200: openapi.Response(
            description="List of goals",
            examples={
                "application/json": {
                    "goals": [
                        {
                            "id": 1,
                            "title": "Sample Goal",
                            "category": "Health",
                            "goal_versions": [
                                {
                                    "id": "abcd1234-5678-efgh-9101-ijklmnopqrst",
                                    "goal": 1,
                                    "level": {
                                        "id": 1,
                                        "rank": 1,
                                        "title": "Beginner"
                                    }
                                }
                            ]
                        }
                    ]
                }
            }
        )
    }
)
class GoalViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows Goals to be viewed or edited.
    """
    queryset = Goal.objects.all()
    serializer_class = GoalSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['id', 'category']

    def get_queryset(self):
        user = self.request.user
        queryset = super().get_queryset()
        all_goals = self.request.query_params.get('all', 'false').lower()
        category = self.request.query_params.get('category', None)
        user_profile = UserProfile.objects.filter(user=user).first()

        if all_goals == 'true':
            return queryset
        if category:
            queryset = queryset.filter(category=category)

        # Filter out all the goals if any goal in that category is already selected by the user
        selected_goals_categories = SelectedGoal.objects.filter(user=user_profile).values_list('goal_version__goal__category', flat=True)

        logger.info(f"User Subscription Tier: {user.subscription_tier}")
        if user.subscription_tier == PlanTier.FREE:
            queryset = queryset.exclude(category__in=selected_goals_categories)

        # Filter out goals that are already selected by the user    
        selected_goals = SelectedGoal.objects.filter(user=user_profile).values_list('goal_version__goal_id', flat=True)
        queryset = queryset.exclude(id__in=selected_goals)

        return queryset

    def perform_create(self, serializer):
        title = serializer.validated_data.get('title')
        category = serializer.validated_data.get('category')

        # Check if a goal with the same title already exists in the given category
        if Goal.objects.filter(title=title, category=category).exists():
            raise ValidationError({"error": 'A goal with this title already exists in the given category.'})

        serializer.save()

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        goals_data = []

        for goal in queryset:
            goal_versions = GoalVersion.objects.filter(goal=goal)
            goal_data = GoalSerializer(goal).data
            goal_data["goal_versions"] = GoalVersionSerializer(goal_versions, many=True).data
            goals_data.append(goal_data)

        return Response({"goals": goals_data})

class SelectedGoalViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing SelectedGoal objects.
    """
    queryset = SelectedGoal.objects.all()
    serializer_class = SelectedGoalSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'id'  # UUID Lookup

    def get_queryset(self):
        """
        Filter queryset based on the authenticated user.
        """
        user_profile = UserProfile.objects.filter(user=self.request.user).first()
        return SelectedGoal.objects.filter(user=user_profile)

    def create(self, request, *args, **kwargs):
        user_profile :UserProfile = UserProfile.objects.filter(user=self.request.user).first()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)  # Validation handled in serializer

        goal_version_id = serializer.validated_data.get("goal_version")
        goal_version = GoalVersion.objects.get(id=goal_version_id.id)
        goal_category = goal_version.goal.category  # Get category from goal_version

        logger.info(f"User Subscription Tier: {request.user.subscription_tier}")
        # Restrict FREE plan users to one goal per category
        if request.user.subscription_tier == PlanTier.FREE:
            if SelectedGoal.objects.filter(user=user_profile, goal_version__goal__category=goal_category).exists():
                return Response({"error": "Free plan users can only select one goal per category."}, status=status.HTTP_400_BAD_REQUEST)

        # Prevent duplicate selection for the same user & goal_version
        if SelectedGoal.objects.filter(user=user_profile, goal_version=goal_version).exists():
            return Response({"error": "You have already selected this goal version."}, status=status.HTTP_400_BAD_REQUEST)
        # Fetch monthly target from GoalVersion
        monthly_target = goal_version.monthly_check_in_limit

        selected_goal = serializer.save(user=user_profile, month_target=monthly_target)
        
        # Update user profile onboarding status
        if not user_profile.is_onboarding_completed:
            send_goal_setting_encouragement_notification(user_profile.user)
            user_profile.is_onboarding_completed = True
            user_profile.save()

        return Response(serializer.data, status=status.HTTP_201_CREATED)

class GoalTrackingView(APIView):
    """
    API to get goal tracking data for a given date range.
    """
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter('start_date', openapi.IN_QUERY, description="Start date in YYYY-MM-DD format", type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('end_date', openapi.IN_QUERY, description="End date in YYYY-MM-DD format", type=openapi.TYPE_STRING, required=True)
        ],
        responses={
            200: openapi.Response(
                description="Goal tracking response",
                examples={
                    "application/json": {
                        "start_date": "2024-12-01",
                        "end_date": "2024-12-07",
                        "goal_tracking": [
                            {"selected_goal_id": "1234abcd-5678-efgh-9101-ijklmnopqrst", "streak": 3, "is_complete": True},
                            {"selected_goal_id": "9876wxyz-5678-efgh-9101-ijklmnopqrst", "streak": 1, "is_complete": False}
                        ]
                    }
                }
            ),
            400: "Bad Request: Invalid parameters"
        }
    )
    def get(self, request):
        user = request.user  # Assuming user is authenticated
        user_profile = UserProfile.objects.get(user=user)
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')

        if not start_date_str or not end_date_str:
            return Response({"error": "Both 'start_date' and 'end_date' parameters are required."}, status=400)

        try:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
        except ValueError:
            return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=400)

        # Fetch goal tracking data within the date range
        tracked_goals: List[GoalTracking] = GoalTracking.objects.filter(
            selected_goal__user=user_profile,
            date__gte=start_date,
            date__lte=end_date
        ).order_by("date")

        # Build response structure with whole GoalTracking object
        goal_tracking_data = GoalTrackingSerializer(tracked_goals, many=True).data

        return Response({
            "start_date": start_date_str,
            "end_date": end_date_str,
            "goal_tracking": goal_tracking_data
        })

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['selected_goal_id', 'tracking_date'],
            properties={
                'selected_goal_id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description="ID of the selected goal"),
                'tracking_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description="Date for tracking in YYYY-MM-DD format"),
                'is_new_week': openapi.Schema(type=openapi.TYPE_BOOLEAN, description="Flag to indicate a new week"),
                'should_reset_goal': openapi.Schema(type=openapi.TYPE_BOOLEAN, description="Flag to reset the goal")
            }
        ),
        responses= {
            200: "Goal tracking created successfully. Returns updated rewards after each check-in",
            400: "Bad Request",
            403: "Forbidden: Check-in not allowed for the given date"
        }
    )
    def post(self, request):
        user_profile = get_object_or_404(UserProfile, user=request.user)
        data = request.data

        selected_goal_id = data.get("selected_goal_id")
        tracking_date_str = data.get("tracking_date")
        is_new_week = data.get("is_new_week", False)
        should_reset_goal = data.get("should_reset_goal", False)

        if not selected_goal_id or not tracking_date_str:
            return Response({"error": "Both 'selected_goal_id' and 'tracking_date' are required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            tracking_date = datetime.strptime(tracking_date_str, "%Y-%m-%d").date()
        except ValueError:
            return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=status.HTTP_400_BAD_REQUEST)

        selected_goal = SelectedGoal.objects.filter(user=user_profile, id=selected_goal_id).first()

        if not selected_goal:
            return Response({"error": "Selected goal not found or does not belong to the authenticated user."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            response_data = track_goal(selected_goal, tracking_date, is_new_week, should_reset_goal)

            # Track goal tracking event in PostHog - temporarily commented out
            # analytics.track_goal_tracking(
            #     user=request.user,
            #     goal_name=selected_goal.goal.title,
            #     tracking_date=tracking_date
            # )

            badges_awarded = BadgeService.check_streak_badges(request.user)

            user_profile.refresh_from_db()
            selected_goal.refresh_from_db()
            
            profile_data = UserProfileSerializer(user_profile).data
            selected_goal_data = {
                **SelectedGoalSerializer(selected_goal).data,
                "goal": GoalSerializer(selected_goal.goal_version.goal).data
            }
            response_data["badges_awarded"]=[]
            for badge in badges_awarded:
                badge_data = UserBadgeSerializer(badge).data
                response_data["badges_awarded"].append(badge_data)
            
            response_data["profile"]= profile_data
            response_data["selected_goal"] = selected_goal_data
            return Response(response_data, status=status.HTTP_201_CREATED)
        except IntegrityError as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except PermissionError as e:
            return Response({"error": str(e)}, status=status.HTTP_403_FORBIDDEN)

@swagger_auto_schema(
    method='get',
    manual_parameters=[
        openapi.Parameter(
            'fcm_token',
            openapi.IN_QUERY,
            description="Optional FCM token for push notifications",
            type=openapi.TYPE_STRING,
            required=False
        )
    ],
    responses={
        200: openapi.Response(
            description="User details, profile, and selected goals",
            examples={
                "application/json": {
                    "user": {
                        "id": 1,
                        "email": "<EMAIL>"
                    },
                    "profile": {
                        "id": 1,
                        "user": 1,
                        "first_name": "John",
                        "last_name": "Doe",
                        "bio": "Sample bio"
                    },
                    "selected_goals": [
                        {
                            "id": "1234abcd-5678-efgh-9101-ijklmnopqrst",
                            "goal_version": {
                                "id": "abcd1234-5678-efgh-9101-ijklmnopqrst",
                                "goal": {
                                    "id": 1,
                                    "title": "Sample Goal",
                                    "category": "Health"
                                },
                                "level": {
                                    "id": 1,
                                    "rank": 1,
                                    "title": "Beginner"
                                }
                            },
                            "month_target": 5
                        }
                    ]
                }
            }
        )
    }
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def me(request):
    """
    Retrieve user details, profile, and selected goals.
    """
    try:
        fcm_token = request.query_params.get('fcm_token', None)

        user = request.user
        user_profile = UserProfile.objects.get(user=user)
        selected_goals = SelectedGoal.objects.filter(user=user_profile)

        user_data = UserSerializer(user).data
        profile_data = UserProfileSerializer(user_profile).data
        selected_goals_data = [
            {
                **SelectedGoalSerializer(goal).data,
                "goal": GoalSerializer(goal.goal_version.goal).data
            }
            for goal in selected_goals
        ]

        response = {
            'user': {
                **user_data,
                'profile': profile_data,
                'selected_goals': selected_goals_data
            }
        }

        # Update or create the UserDevice instance with the FCM token
        if fcm_token:
            UserDevice.objects.update_or_create(
                user=user,
                fcm_token=fcm_token
            )

        return Response(data=response, status=status.HTTP_200_OK)

    except UserProfile.DoesNotExist:
        return Response(
            {"error": "User profile not found."},
            status=status.HTTP_404_NOT_FOUND
        )

    except S3OperationError as e:
        return Response(
            {"error": f"Could not generate profile image URL: {str(e)}"},
            status=status.HTTP_503_SERVICE_UNAVAILABLE
        )

    except ValidationError as e:
        return Response(
            {"error": "Validation failed", "details": e.detail},
            status=status.HTTP_400_BAD_REQUEST
        )

    except Exception as e:
        return Response(
            {"error": "An unexpected error occurred."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

class SelectGoalView(APIView):
    """
    API endpoint for selecting a goal.
    """
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['goal_version_id', 'target'],
            properties={
                'goal_version_id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description="ID of the goal version"),
                'target': openapi.Schema(type=openapi.TYPE_INTEGER, description="Target for the goal (1-7)")
            }
        ),
        responses={
            201: SelectedGoalSerializer(),
            400: "Bad Request: Invalid parameters"
        }
    )
    def post(self, request):
        user_profile = UserProfile.objects.filter(user=self.request.user).first()
        goal_version_id = request.data.get("goal_version_id")
        target = request.data.get("target")

        if not goal_version_id or target is None:
            return Response({"error": "Both 'goal_version_id' and 'target' are required."}, status=status.HTTP_400_BAD_REQUEST)

        if target < 1 or target > 7:
            return Response({"error": "Target must be between 1 and 7."}, status=status.HTTP_400_BAD_REQUEST)

        goal_version = get_object_or_404(GoalVersion, id=goal_version_id)
        goal = goal_version.goal
        goal_category = goal.category

        logger.info(f"User Subscription Tier: {request.user.subscription_tier}")
        # Restrict FREE plan users to one goal per category
        if request.user.subscription_tier == PlanTier.FREE:
            if SelectedGoal.objects.filter(user=user_profile, goal__category=goal_category).exists():
                return Response({"error": "Free plan users can only select one goal per category."}, status=status.HTTP_400_BAD_REQUEST)

        # Prevent duplicate selection for the same user & goal
        if SelectedGoal.objects.filter(user=user_profile, goal=goal).exists():
            return Response({"error": "You have already selected this goal."}, status=status.HTTP_400_BAD_REQUEST)

        selected_goal = SelectedGoal.objects.create(
            user=user_profile,
            goal_version=goal_version,
            goal=goal,
            target=target
        )

        # Track goal selection in PostHog - temporarily commented out
        # analytics.track_event(
        #     user=request.user,
        #     event_name='Goal Selected',
        #     properties={
        #         'goal_name': goal.title,
        #         'goal_category': goal.category,
        #         'target': target,
        #         'level': goal_version.level,
        #     }
        # )

        # Update user profile onboarding status
        if not user_profile.is_onboarding_completed:
            user_profile.is_onboarding_completed = True
            user_profile.save()

            # Track onboarding completion - temporarily commented out
            # analytics.track_event(
            #     user=request.user,
            #     event_name='Onboarding Completed',
            #     properties={}
            # )

        selected_goal_data = SelectedGoalSerializer(selected_goal).data
        selected_goal_data['goal_version'] = GoalVersionSerializer(goal_version).data
        selected_goal_data['goal'] = GoalSerializer(goal).data
        user_data = UserSerializer(request.user).data
        profile_data = UserProfileSerializer(user_profile).data
        selected_goals = SelectedGoal.objects.filter(user=user_profile)
        selected_goals_data = [
            {
            **SelectedGoalSerializer(goal).data,
            "goal": GoalSerializer(goal.goal_version.goal).data
            }
            for goal in selected_goals
        ]

        response = {'user': {**user_data, 'profile': profile_data, "selected_goals": selected_goals_data}}

        return Response(response, status=status.HTTP_201_CREATED)


class EditGoalView(APIView):
    """
    API endpoint to edit goal target
    """
    
    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["target"],
            properties={
                "target": openapi.Schema(type=openapi.TYPE_INTEGER, description="New target value"),
            },
        ),
        responses={
            200: "Goal target successfully",
            400: "Bad Request",
        }
    )
    def patch(self, request, id, *args, **kwargs):
        """
        Allows users to update only the 'target' field of their own SelectedGoal.
        """
        user_profile = UserProfile.objects.filter(user=self.request.user).first()
        target = request.data.get("target")
    
        if not id:
            return Response({"error": "Please specify selected goal id to modify" }, status=status.HTTP_400_BAD_REQUEST)
        
        if target is None:
            return Response({"error": "The field 'target' is required."}, status=status.HTTP_400_BAD_REQUEST)

        if target < 1 or target > 7:
            return Response({"error": "Target must be between 1 and 7."}, status=status.HTTP_400_BAD_REQUEST)

        selected_goal = SelectedGoal.objects.filter(user=user_profile, id=id).first()
        
        if not selected_goal:
            return Response({"error": "Selected goal not found or does not belong to the authenticated user."}, status=status.HTTP_400_BAD_REQUEST)

        response_data = edit_goal(selected_goal, target)

        user_profile.refresh_from_db()
        selected_goal.refresh_from_db()

        profile_data = UserProfileSerializer(user_profile).data
        selected_goal_data = {
            **SelectedGoalSerializer(selected_goal).data,
            "goal": GoalSerializer(selected_goal.goal_version.goal).data
        }


        response_data["profile"]= profile_data
        response_data["selected_goal"] = selected_goal_data

        return Response(response_data, status=status.HTTP_200_OK)


class RestoreGoalView(APIView):
    """
    API endpoint to restore streak of the selected user goal
    """
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['selected_goal_id', 'restore_date'],
            properties={
                'selected_goal_id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description="ID of the selected user goal"),
                'restore_date': openapi.Schema(type=openapi.TYPE_STRING, description="Restoration date in YYYY-MM-DD format")
            }
        ),
        responses={
            200: "Goal streak restored successfully",
            400: "Bad Request: Invalid parameters or Not enough points to restore",
            403: "Forbidden: Unable to restore streak. Please try again!",
            500: "Internal Server Error: Unexpected error occurred"
        }
    )
    def post(self, request):

        user_profile = UserProfile.objects.filter(user=self.request.user).first()
        restore_date_str = request.data.get("restore_date")
        selected_goal_id = request.data.get("selected_goal_id")

        if not restore_date_str:
            return Response({"error": "'restore_date' is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            restore_date = datetime.strptime(restore_date_str, "%Y-%m-%d").date()
        except ValueError:
            return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=status.HTTP_400_BAD_REQUEST)

        if not selected_goal_id:
            return Response({"error": "Please specify selected goal id" }, status=status.HTTP_400_BAD_REQUEST)

        selected_goal = SelectedGoal.objects.filter(user=user_profile, id=selected_goal_id).first()
        
        if not selected_goal:
            return Response({"error": "Selected goal not found or does not belong to the authenticated user."}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            restore_streak(selected_goal, restore_date)
        except PermissionError as e:
            return Response({"error": str(e)}, status=status.HTTP_403_FORBIDDEN)
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        selected_goal_data = {
            **SelectedGoalSerializer(selected_goal).data,
            "goal": GoalSerializer(selected_goal.goal_version.goal).data
        }

        return Response(selected_goal_data, status=status.HTTP_200_OK)

class ProfilePictureUploadView(APIView):
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    @swagger_auto_schema(
        request_body=ProfilePictureUploadSerializer,
        responses={
            200: openapi.Response("Returns updated user profile object", UserProfileSerializer),
            400: "Bad Request: Invalid parameters",
            500: "Internal Server Error: S3 upload failed"
        }
    )
    def post(self, request):
        serializer = ProfilePictureUploadSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            profile = UserProfile.objects.get(user=request.user)
            file_obj = serializer.validated_data['profile_picture']

            new_key, _ = upload_s3_file(file_obj)

            # Upload success → now delete old image
            if profile.profile_image_path:
                delete_s3_object(profile.profile_image_path)

            profile.profile_image_path = new_key
            profile.save()

            return Response(UserProfileSerializer(profile).data, status=status.HTTP_200_OK)

        except UserProfile.DoesNotExist:
            return Response({"error": "User profile not found."}, status=status.HTTP_404_NOT_FOUND)

        except S3OperationError as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({"error": "Unexpected server error."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
@swagger_auto_schema(
    method='post',
    request_body=FeedbackEmailSerializer,
    responses={200: 'Feedback mail has been sent successfully', 400: 'Bad Request', 500: 'Internal Server Error'}
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_feedback_email(request):
    """
    Send email of user's feedback
    """
    user = request.user
    serializer = FeedbackEmailSerializer(data=request.data)
    if not serializer.is_valid():
        logger.warning(f"Feedback validation failed: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    # comment can be null
    # score is required
    comment = serializer.validated_data.get('comment', "")
    score = serializer.validated_data['score']

    profile = get_object_or_404(UserProfile, user=user)

    context = {
        'user_name': profile.full_name,
        'email': user.email,
        'comment': comment,
        'score': score,
    }

    try:
        email_body = render_to_string('feedback-form.html', context)

        send_email(
            sender=settings.DEFAULT_FROM_EMAIL,
            recipient=[settings.SUPPORT_MAIL_RECIPIENT],
            subject=f'New User Feedback Submitted - NPS Score {score}',
            html_content=email_body,
        )

        Feedback.objects.create(
            user=user,
            comment=comment,
            score=score
        )

        return Response({"message": "Feedback mail has been sent successfully"}, status=status.HTTP_200_OK)

    except ValidationError as e:
        logger.error(f"Validation error while sending feedback: {e}")
        return Response({"message": f"Validation error: {e}"}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.exception("Unexpected error while sending feedback email")
        return Response({"message": f"Error sending mail: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
class CustomPageNumberPagination(PageNumberPagination):
    page_size = 25 # Default page size
    page_query_param = 'page'
    page_size_query_param = 'limit'  # Allow dynamic page size via `limit`
    max_page_size = 100

    def get_paginated_response(self, data):
        return Response({
            'count': self.page.paginator.count,
            'page': self.page.number,
            'limit': self.get_page_size(self.request),
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'results': data
        })
    
class UserNotificationListView(ListAPIView):
    serializer_class = UserNotificationSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination

    def get_queryset(self):
        return UserNotification.objects.filter(user=self.request.user).select_related('notification').order_by('-timestamp') 

class UpdateFCMTokenView(APIView):
    """
    API endpoint to update the FCM token for a specific UserDevice.
    """
    
    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "fcm_token": openapi.Schema(type=openapi.TYPE_STRING, description="New FCM token"),
            },
        ),
        responses={
            200: "FCM token updated successfully",
            400: "Bad Request",
            404: "UserDevice not found",
        }
    )
    def patch(self, request, *args, **kwargs):
        """
        Allows users to update the 'fcm_token' field of their own UserDevice.
        """
        try:
            user = request.user
            
            fcm_token = request.data.get("fcm_token")
            
            if fcm_token:
                
                UserDevice.objects.update_or_create(
                    user=user,
                    fcm_token=fcm_token
                )

                return Response({"message": "FCM token updated successfully."}, status=status.HTTP_200_OK)
                
            return Response({"message": "No FCM provided, skipping updation"}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"An error occurred while updating the FCM token: {e}")
            return Response({"error": "An unexpected error occurred. Please try again later."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class LogoutView(APIView):
    """
    API endpoint to log out a user.
    """
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "fcm_token": openapi.Schema(type=openapi.TYPE_STRING, description="FCM token to unregister"),
            },
        ),
        responses={
            200: "Logout successful",
            400: "Bad Request: Invalid parameters",
            404: "UserDevice not found",
        }
    )

    # TODO: Blacklist access token
    def post(self, request, *args, **kwargs):
        try:
            user = request.user
            fcm_token = request.data.get("fcm_token")

            if fcm_token:
                # Fetch the UserDevice object
                user_device = UserDevice.objects.filter(user=user, fcm_token=fcm_token).first()

                if not user_device:
                    return Response({"error": "Provided FCM token not registered with the user."}, status=status.HTTP_404_NOT_FOUND)

                # Delete the UserDevice record
                user_device.delete()

            return Response({"message": "Logout successful."}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"An error occurred during logout: {e}")
            return Response({"error": "An unexpected error occurred. Please try again later."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ResetChatView(APIView):
    """
    API endpoint to reset the chat for all users.
    """
    permission_classes = [IsAdminUser]

    def post(self, request):
        try:
            UserProfile.objects.all().update(is_chat_reset=True)
            return Response({"message": "All user chats have been reset."}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



class SubscriptionPlansView(APIView):

    @swagger_auto_schema(
        operation_summary="Get subscription tiers grouped with pricing, features, and colors",
        responses={200: openapi.Response('Grouped Subscriptions', SubscriptionTierSerializer(many=True))}
    )
    def get(self, request):
        tier_groups = {}

        plans = SubscriptionPlan.objects.all()

        for plan in plans:
            tier = (plan.tier or "FREE").upper()

            if tier not in tier_groups:
                features = PlanFeature.objects.filter(tier__iexact=tier)
                tier_groups[tier] = {
                    "tier": tier.lower(),
                    "features": PlanFeatureSerializer(features, many=True).data,
                    "yearly": None,
                    "monthly": None,
                    "quarterly": None,
                    "colors": [f"{tier.lower()}GradientStart", f"{tier.lower()}GradientEnd"]
                }

            billing_key = plan.billing_period.lower() if plan.billing_period else None
            if billing_key in ["yearly", "monthly", "quarterly"]:
                tier_groups[tier][billing_key] = {
                    "product_id": plan.product_id,
                    "price": plan.price
                }

        return Response(list(tier_groups.values()), status=status.HTTP_200_OK)


class AccountDeletionRequestView(APIView):
    """Handles account deletion requests and sends a confirmation email."""

    @swagger_auto_schema(request_body=EmailSerializer, operation_id="account-deletion-request")
    def post(self, request):
        serializer = EmailSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        email = serializer.validated_data.get("email")
        user = User.objects.filter(email=email).first()
        if not user:
            return Response({'message': 'User not found.'}, status=status.HTTP_404_NOT_FOUND)

        full_name = UserProfile.objects.filter(user=user).values_list("full_name", flat=True).first() or "User"
        first_name = full_name.split()[0] if full_name else "User"
        token = generate_token(user)
        # Send deletion request email
        self.send_deletion_email(first_name, email, token, request)

        return Response({'message': 'Account deletion email sent successfully.'}, status=status.HTTP_200_OK)

    def send_deletion_email(self, first_name, recipient_email, token, request):
        """Sends the deletion confirmation/request email to the user."""
        html_content = render_to_string(
            "delete-account.html",
            {"first_name": first_name, "token": token, "api_base_url": get_host_url(request)}
        )

        send_email(
            sender=settings.EMAIL_HOST_USER,
            recipient=[recipient_email],
            subject="Account Deletion Request",
            html_content=html_content,
            text_content="This is a confirmation that we have received your account deletion request. Please follow the instructions in the email to proceed with the deletion.",
        )

class DeleteAccountView(APIView):
    """Handles user deletion via token."""

    @swagger_auto_schema(
        operation_id="delete-account",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["token"],
            properties={
                'token': openapi.Schema(type=openapi.TYPE_STRING, description='Token in the format "<uid-token>"')
            }
        )
    )
    def delete(self, request):
        token = request.query_params.get('token')[6:]
        if not token:
            return Response({'error': 'Token must be provided in format: Token <uid-token>'}, status=status.HTTP_400_BAD_REQUEST)


        uid, token = self.decode_token(token)
        
        user = User.objects.filter(pk=uid).first()
        if not user or not default_token_generator.check_token(user, token):
            return Response({'error': 'Invalid token or user not found'}, status=status.HTTP_400_BAD_REQUEST)

        user.delete()

        return Response({'message': 'User and associated data deleted successfully'}, status=status.HTTP_200_OK)

    
    def decode_token(self, token):
        """Decodes the user ID from the token."""

        try:
            uid, token = token.split('-', 1)
            return urlsafe_base64_decode(uid).decode('utf-8'), token
        except (ValueError, TypeError):
            return Response({'error': 'Invalid token'}, status=status.HTTP_400_BAD_REQUEST)
    

@api_view(['POST'])
def chat_view(request):
    user_query = request.data.get('message')

    def chat_generator():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        async_gen = chat_interactor.call(
            db=None,
            request=request,
            vector_db=WeaviateDB,
            namespace="cd-1",
            query=user_query
        )
        
        try:
            while True:
                chunk = loop.run_until_complete(async_gen.__anext__())
                print("Sending chunk:", chunk)  # Debug print
                yield f"data: {json.dumps(chunk)}\n\n"
        except StopAsyncIteration:
            pass
        finally:
            loop.close()

    response = StreamingHttpResponse(
        chat_generator(),
        content_type="text/event-stream"
    )
    response['Cache-Control'] = 'no-cache'
    response['X-Accel-Buffering'] = 'no'
    return response

@api_view(['POST'])
def apple_iap_webhook(request):
    """
    Endpoint for Apple App Store Server Notifications (v2)
    Handles server-to-server notifications about in-app purchase events.
    """
    try:
        # Parse and log the incoming payload
        payload = json.loads(request.body.decode('utf-8'))
        logger.info("Received Apple IAP notification", extra={'payload': payload})

        # Verify the notification if enabled in settings
        # if settings.APPLE_APP_STORE.get('VERIFICATION_ENABLED', False):
        #     if not verify_apple_notification(payload):
        #         logger.error("Apple notification verification failed")
        #         return JsonResponse(
        #             {'status': 'error', 'message': 'Verification failed'},
        #             status=400
        #         )

        # Extract and decode the signed payload
        if 'signedPayload' not in payload:
            logger.error("Missing signedPayload in Apple notification")
            return JsonResponse({'status': 'error', 'message': 'Missing signedPayload'}, status=status.HTTP_400_BAD_REQUEST)

        decoded_payload = decode_signed_data(payload['signedPayload'])
        logger.debug("Decoded Apple notification payload", extra={'decoded_payload': decoded_payload})

        # Extract notification metadata
        notification_type = decoded_payload.get('notificationType')
        subtype = decoded_payload.get('subtype')

        # Extract transaction info
        if 'data' not in decoded_payload or 'signedTransactionInfo' not in decoded_payload['data']:
            logger.error("Missing transaction info in Apple notification")
            return JsonResponse({'status': 'error', 'message': 'Missing transaction info'}, status=status.HTTP_400_BAD_REQUEST)

        transaction_info = decode_signed_data(decoded_payload['data']['signedTransactionInfo'])
        logger.debug("Decoded transaction info", extra={'transaction_info': transaction_info})

        # Get user from transaction (implement your own logic here)
        user = get_user_from_transaction(transaction_info)
        if not user:
            logger.error("Could not determine user from transaction", extra={'transaction_id': transaction_info.get('originalTransactionId')})
            return JsonResponse({'status': 'error', 'message': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

        # Process the event using our processor class
        processor = PurchaseProcessor(
            user=user,
            event_data={**decoded_payload, **transaction_info},
            product_id=transaction_info.get('productId')
        )

        try:
            subscription = processor.process_event()
        except ValueError as ve:
            # Handle the case when subscription is not found gracefully
            logger.warning(f"Processor error: {str(ve)}")
            return JsonResponse({'status': 'error', 'message': str(ve)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            # Catch all unexpected processor errors
            logger.error(f"Unhandled processor error: {str(e)}", exc_info=True)
            return JsonResponse({'status': 'error', 'message': 'Internal processing error'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        response_data = {
            'status': 'success',
            'notification_type': notification_type,
            'subtype': subtype,
            'user_id': user.id,
            'subscription_id': str(subscription.id),
            'transaction_id': transaction_info.get('originalTransactionId'),
            'product_id': transaction_info.get('productId'),
            'expiry_date': subscription.expiry_time.isoformat() if subscription.expiry_time else None
        }

        logger.info("Successfully processed Apple IAP notification", extra={'response_data': response_data})
        return JsonResponse(response_data, status=status.HTTP_200_OK)

    except json.JSONDecodeError:
        logger.error("Invalid JSON payload received", exc_info=True)
        return JsonResponse({'status': 'error', 'message': 'Invalid JSON payload'}, status=status.HTTP_400_BAD_REQUEST)

    except KeyError as e:
        logger.error(f"Missing required field in payload: {str(e)}", exc_info=True)
        return JsonResponse({'status': 'error', 'message': f'Missing required field: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error processing Apple IAP notification: {str(e)}", exc_info=True)
        return JsonResponse({'status': 'error', 'message': 'Internal server error'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GooglePlayRTDNWebhook(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        try:
            payload = request.data
            pubsub_message = payload.get('message', {})
            base64_data = pubsub_message.get('data')

            if not base64_data:
                return Response({'status': 'error', 'message': 'No data field found.'}, status=status.HTTP_400_BAD_REQUEST)

            decoded_bytes = base64.b64decode(base64_data)
            decoded_str = decoded_bytes.decode('utf-8')
            subscription_notification = json.loads(decoded_str)

            logger.info(f"Received Google Play RTDN: {subscription_notification}")

            subscription_data = subscription_notification.get('subscriptionNotification', {})
            notification_type = subscription_data.get('notificationType')
            purchase_token = subscription_data.get('purchaseToken')
            package_name = subscription_notification.get('packageName')

            if not purchase_token or not package_name:
                logger.error("Missing purchaseToken or packageName in notification.")
                return Response({'status': 'error', 'message': 'Missing purchaseToken or packageName.'}, status=status.HTTP_400_BAD_REQUEST)

            subscription_details = self.fetch_subscription_details(package_name, purchase_token)
            if not subscription_details:
                logger.error("Failed to fetch subscription details from Google Play API.")
                return Response({'status': 'error', 'message': 'Failed to fetch subscription details.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            user = self.get_user_from_subscription(subscription_details)
            if not user:
                logger.warning("User not found for the subscription.")
                return Response({'status': 'error', 'message': 'User not found.'}, status=status.HTTP_200_OK)

            processor = GooglePurchaseProcessor(user, subscription_details, notification_type, purchase_token)
            processor.process_event()

            logger.info("Google Play RTDN processed successfully.")
            return Response({'status': 'success', 'result': subscription_notification}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.exception("Error processing Google Play RTDN")
            return Response({'status': 'error', 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def fetch_subscription_details(self, package_name, purchase_token):
        access_token = self.get_google_api_access_token()
        url = f"https://androidpublisher.googleapis.com/androidpublisher/v3/applications/{package_name}/purchases/subscriptionsv2/tokens/{purchase_token}"

        headers = {
            "Authorization": f"Bearer {access_token}"
        }

        response = requests_lib.get(url, headers=headers)
        if response.status_code == 200:
            response_data = response.json()
            logger.info(f"Successfully fetched subscription details from Google Play API: {response_data}")
            return response_data
        else:
            logger.error(f"Google Play API returned {response.status_code}: {response.text}")
            return None

    def get_google_api_access_token(self):
        credentials = service_account.Credentials.from_service_account_file(
            settings.GOOGLE_PLAY_SERVICE_ACCOUNT_FILE,
            scopes=["https://www.googleapis.com/auth/androidpublisher"]
        )
        credentials.refresh(requests.Request())
        return credentials.token

    def get_user_from_subscription(self, subscription_details):
        external_id = subscription_details.get('externalAccountIdentifiers', {}).get('obfuscatedExternalAccountId')

        if external_id:
            user = User.objects.filter(id=external_id).first()
            if user:
                logger.info(f"User found with external_account_id: {external_id}")
                return user

        logger.warning("No matching user found for externalAccountId.")
        return None


class UserBadgeViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = UserBadgeSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserBadge.objects.filter(user=self.request.user)


# Temporarily commented out AnalyticsView for startup
# class AnalyticsView(APIView):
    """
    API endpoint for analytics data combining PostHog and PostgreSQL data.
    Implements the Query-as-Needed approach.
    """
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter('days', openapi.IN_QUERY, description="Number of days to look back (default: 30)", type=openapi.TYPE_INTEGER, required=False),
            openapi.Parameter('type', openapi.IN_QUERY, description="Analytics type: 'summary', 'goals', or 'journey'", type=openapi.TYPE_STRING, required=False),
            openapi.Parameter('goal_id', openapi.IN_QUERY, description="Specific goal ID for goal analytics", type=openapi.TYPE_STRING, required=False),
        ],
        responses={
            200: openapi.Response(
                description="Analytics data",
                examples={
                    "application/json": {
                        "user_id": "123e4567-e89b-12d3-a456-426614174000",
                        "type": "summary",
                        "data": {
                            "postgresql_data": {
                                "total_goals": 3,
                                "total_checkins": 25,
                                "user_points": 150
                            },
                            "posthog_data": {
                                "total_events": 45
                            }
                        }
                    }
                }
            ),
            400: "Bad Request: Invalid parameters"
        }
    )
    def get(self, request):
        """
        Get analytics data for the authenticated user.

        Query parameters:
        - days: Number of days to look back (default: 30)
        - type: Analytics type - 'summary', 'goals', or 'journey' (default: 'summary')
        - goal_id: Specific goal ID for goal analytics (only for type='goals')
        """
        user = request.user
        days = int(request.query_params.get('days', 30))
        analytics_type = request.query_params.get('type', 'summary')
        goal_id = request.query_params.get('goal_id')

        try:
            if analytics_type == 'summary':
                data = analytics_queries.get_user_analytics_summary(user, days)
            elif analytics_type == 'goals':
                data = analytics_queries.get_goal_analytics(user, goal_id)
            elif analytics_type == 'journey':
                data = analytics_queries.get_user_journey_data(user)
            else:
                return Response(
                    {"error": "Invalid analytics type. Use 'summary', 'goals', or 'journey'."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            return Response({
                'user_id': str(user.id),
                'type': analytics_type,
                'data': data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error retrieving analytics for user {user.id}: {e}")
            return Response(
                {"error": "Failed to retrieve analytics data"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )