import logging
import boto3
from django.conf import settings

from .firebase_service import send_multicast_notifications

# Initialize logger
logger = logging.getLogger(__name__)

# SQS client initialization
sqs_client = boto3.client('sqs', region_name=settings.AWS_REGION)  # Use AWS_REGION if defined

# Helper function to get queue URL from SQS
def get_queue_url(queue_name: str):
    try:
        response = sqs_client.get_queue_url(QueueName=queue_name)
        logger.info(f"Queue URL for {queue_name}: {response['QueueUrl']}")
        return response['QueueUrl']
    except sqs_client.exceptions.QueueDoesNotExist:
        logger.error(f"Queue {queue_name} does not exist.")
        raise
    except Exception as e:
        logger.error(f"Error getting queue URL for {queue_name}: {e}")
        raise

# Helper function to receive messages from an SQS queue
def receive_sqs_messages(queue_name: str, max_messages=10, wait_time=2):
    
    try:
        queue_url = get_queue_url(queue_name)

        response = sqs_client.receive_message(
            QueueUrl=queue_url,
            MaxNumberOfMessages=max_messages,
            WaitTimeSeconds=wait_time,
            VisibilityTimeout=60
        )
        return response.get('Messages', [])
    except Exception as e:
        logger.error(f"Error receiving messages from {queue_name}: {e}")
        return []

# Helper function to delete a message from an SQS queue
def delete_sqs_message(queue_name: str, receipt_handle: str):
    try:
        queue_url = get_queue_url(queue_name)

        sqs_client.delete_message(
            QueueUrl=queue_url,
            ReceiptHandle=receipt_handle
        )
        logger.info(f"Deleted message from {queue_name}")
    except Exception as e:
        logger.error(f"Error deleting message from {queue_name}: {e}")
