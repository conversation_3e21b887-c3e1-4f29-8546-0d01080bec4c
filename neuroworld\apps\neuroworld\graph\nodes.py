import os
from datetime import datetime
from .state import State
from ..models import <PERSON>, <PERSON><PERSON>, SelectedGoal, GoalVersion, UserProfile
from ..services.llm import LLM
from ..chat_utils.prompt_hub import PromptHub
from asgiref.sync import sync_to_async

async def select_category_node(state: State):
    state['ai_reply'] = [
        {   
            "text": f"I'd love to help you pick a new habit! First, let me ask a few questions to find the best category for you. Which area would you like to focus on improving?",
            "avatar": "Myla",
            "actionType": "List",
            "actions": [
                "Nutrition - Eating healthier foods",
                "Exercise - Moving more",
                "Unwind - Reducing stress",
                "Restore - Better sleep",
                "Optimize - Mental performance"
            ]
        }
    ]
    state['executed_nodes'].append('select_category_node')
    return state

async def goals_options_node(state: State):
    messages = PromptHub.category_selector(state['user_query'])
    response = LLM.ask_from_gpt(messages=messages)
    
    if 'NONE' in response:
        state['ai_reply'] = [
            {
            "text": "I'd love to help you pick a new habit! First, let me ask a few questions to find the best category for you. Which area would you like to focus on improving?",
            "avatar": "Myla",
            "actionType": "List",
            "actions": [
                "Nutrition - Eating healthier foods",
                "Exercise - Moving more",
                "Unwind - Reducing stress",
                "Restore - Better sleep",
                "Optimize - Mental performance"
            ]
            }
        ]
    else:
        state['executed_nodes'].append('goals_options_node')
        index = 1
        goal_list_text = ""
        attrs = state['chat'].attrs
        attrs['goal_action_category'] = response
        # Chat.objects.filter(id=state['chat'].id).update(**attrs) #TODO: Fix this line to update the chat attributes correctly
        goal_list = await sync_to_async(list)(
            Goal.objects.filter(category=response.split(' - ')[0].lower()).values_list('title', flat=True)
        )
        for goal in goal_list:
            goal_list_text += f"\n{index}. [{goal}]"
            index += 1

        state['ai_reply'] = [
            {
            "text": f"Here are some goal suggestions based on your concern: {goal_list_text} Which goal would you like to add?",
            "avatar": "Myla",
            "actionType": "List",
            "actions": goal_list
            }
        ]
    return state

async def character_switch_node(state: State):
    messages = PromptHub.goals_options_selector(
        user_query=state['user_query'],
        goal_options=state['llm_last_message'],
        selected_category = state['chat'].attrs['goal_action_category']
    )
    response = LLM.ask_from_gpt(messages=messages)
    print("\n\n\n", response, "\n\n\n")
    if 'NONE' in response:
        print(state['llm_last_message'])
        state['ai_reply'] = [state['llm_last_message']]
    else:
        character, goal_name = response.split(',')
        character = character.strip()
        goal = await sync_to_async(Goal.objects.get)(title=goal_name.strip())
        attrs = state['chat'].attrs
        attrs['goal_uuid'] = str(goal.id)
        # state['chat'] = await sync_to_async(Chat.objects.filter(id=state['chat'].id).update)(**attrs)
        state['executed_nodes'].append('character_switch_node')
        state['character'] = character
        state['ai_reply'] =[
            {
                "text": f"Great choice! {character} will guide you in setting up this goal.",
                "avatar": f"{character}",
                "actionType": "None",
                "actions": []
            },
            {
                "text": "How many days per week would you like to work on this goal?",
                "avatar": f"{character}",
                "actionType": "List",
                "actions": ["1", "2", "3", "4", "5", "6", "7"]
            }
        ]
    return state

async def days_selection_node(state: State):
    messages = PromptHub.days_selection(state['user_query'])
    response = LLM.ask_from_gpt(messages=messages)
    
    character = state['chat'].attrs['history'][-1]['character']
    
    if 'NONE' in response:
        state['character'] = character
        state['ai_reply'] = [
            {
            "text": f"How many days per week would you like to work on this goal?",
            "avatar": f"{character}",
            "actionType": "List",
            "actions": ["1", "2", "3", "4", "5", "6", "7"]
            }
        ]
    else:
        state['character'] = 'Myla'
        state['executed_nodes'].append('days_selection_node')
        state['ai_reply'] = [
            {
            "text": "Awesome! You're all set to start this goal. Ready to commit?",
            "avatar": "Myla",
            "actionType": "Button",
            "actions": ["Sure, let's do it!"]
            }
        ]
    return state

async def confirmation_question_node(state: State):
    messages = PromptHub.confirmation_question_selector(state['user_query'])
    response = LLM.ask_from_gpt(messages=messages)
    
    character = state['chat'].attrs['history'][-1]['character']
    
    if 'NONE' in response:
        state['character'] = character
        state['ai_reply'] = [
            {
            "text": "Awesome! You're all set to start this goal. Ready to commit?",
            "avatar": "Myla",
            "actionType": "Button",
            "actions": ["Sure, let's do it!"]
            }
        ]
    elif 'YES' in response:
        goal_version = await sync_to_async(lambda: GoalVersion.objects.filter(goal=state['chat'].attrs['goal_uuid']).first())()
        user_profile = await sync_to_async(lambda: UserProfile.objects.filter(user=state['chat'].user_id).first())()
        goal = await sync_to_async(Goal.objects.get)(id=state['chat'].attrs['goal_uuid'])
        await sync_to_async(SelectedGoal.objects.create)(
            user=user_profile,
            goal=goal,
            goal_version=goal_version,
            target= int(state["conversation_history"][2]['user_query'])

        )
        state['action_in_progress'] = None
        state['executed_nodes'] = []
        state['character'] = 'Myla'
        state['ai_reply'] = [
            {
            "text": "Great! Your goal is now set. Stay consistent and track your progress!",
            "avatar": "Myla",
            "actionType": "None",
            "actions": []
            },
            {
            "text": "View your goals",
            "avatar": "Myla",
            "actionType": "CTA",
            "actions": ["Check out your goal"]
            }
            # {
            # "text": "Your journey is taking shape! Tap below to explore your city.",
            # "avatar": "Myla",
            # "actionType": "CTA",
            # "actions": ["Explore the city!"]
            # }
        ]
    else:
        state['action_in_progress'] = None
        state['executed_nodes'] = []
        state['character'] = 'Myla'
        state['ai_reply'] = [
            {
            "text": "Got it, Sorry to hear that you are not going to start it now.",
            "avatar": "Myla",
            "actionType": "None",
            "actions": []
            }
        ]
    return state
