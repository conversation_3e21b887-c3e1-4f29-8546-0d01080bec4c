# Generated by Django 4.2.18 on 2025-06-03 12:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('neuroworld', '0032_rename_apple_product_id_subscriptionplan_product_id_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='planfeature',
            name='tier',
            field=models.CharField(choices=[('free', 'Free'), ('habits', 'Habits'), ('summit', 'Summit'), ('clinical', 'Clinical')], max_length=20),
        ),
        migrations.AlterField(
            model_name='subscriptionplan',
            name='tier',
            field=models.CharField(choices=[('free', 'Free'), ('habits', 'Habits'), ('summit', 'Summit'), ('clinical', 'Clinical')], max_length=20),
        ),
        migrations.AlterField(
            model_name='user',
            name='plan',
            field=models.CharField(choices=[('free', 'Free'), ('habits', 'Habits'), ('summit', 'Summit'), ('clinical', 'Clinical')], default='free', max_length=50),
        ),
    ]
