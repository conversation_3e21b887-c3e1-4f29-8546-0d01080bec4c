{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
  {% csrf_token %}
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Privacy Policy - Neuroworld</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      line-height: 1.6;
      background-color: #f9f9f9;
      color: #4B4B4B;
    }
    .container {
      max-width: 960px;
      margin: auto;
      padding: 2em;
      background: white;
    }
    h1, h2, h3, h4 {
      color: #16191D;
      margin-bottom: 0;
      line-height: 1.2;
    }
    h4 {
      margin-top: 0.5em;
    }
    p {
      margin-top: 0.5em;
    }
    ul {
      padding-left: 1.2em;
    }
    .text-center {
      text-align: center;
    }
    a.link {
      color: #4B4B4B;
    }
    .email-input {
      border: 1px solid #D3D8DE;
      border-radius: 8px; 
      padding: 1em;
    }
    .submit-button {
      border: 0;
      border-bottom: 4px solid #DF7802;
      border-radius: 8px;
      background-color: #FF9317;
      padding: 1em;
      color: white;
      cursor: pointer;
    }
    .flex-div {
      display: flex;
      flex-direction: column;
      gap: 15px;
      margin-top: 2em;
    }
    .success-message {
      color: green;
      margin-top: 0.5em;
      text-align: center;
      font-size: 12px;
    }
    .error-message {
      color: red;
      margin-top: 0.5em;
      text-align: center;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="text-center">Account Deletion and Data Removal</h1>
    <hr/>
    <p>When you choose to delete your account, your data will be permanently removed from our systems. This process is irreversible and includes the deletion of the following information:</p>
    <h4>Personal Information:</h4>
    <ul>
      <li>Name</li>
      <li>Email Address</li>
      <li>Country</li>
      <li>Age</li>
    </ul>
    <h4>Account-Related Data:</h4>
    <ul>
      <li>User account instance and password</li>
      <li>Goals and progress tracking</li>
      <li>Points earned</li>
      <li>Badges collected</li>
      <li>Neuro City assets and related content</li>
      <li>AI chat data, including chat history</li>
    </ul>
    <p>This action is final and cannot be undone. In accordance with GDPR and CCPA guidelines, your data will no longer be stored or processed after deletion.</p>
    
    <form class="flex-div" id="delete-form">
      <input type="text" id="email" placeholder="Enter your registered email address" class="email-input" required />
      <button type="submit" class="submit-button">Submit</button>
      <div id="response-message"></div>
    </form>
  </div>

  <script>
    // CSRF helper
    function getCookie(name) {
      let cookieValue = null;
      if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
          cookie = cookie.trim();
          if (cookie.startsWith(name + '=')) {
            cookieValue = decodeURIComponent(cookie.slice(name.length + 1));
            break;
          }
        }
      }
      return cookieValue;
    }

    const csrftoken = getCookie('csrftoken');
    const API_BASE_URL = "{{ api_base_url }}";

    const form = document.getElementById('delete-form');
    const emailInput = document.getElementById('email');
    const responseMessage = document.getElementById('response-message');

    form.addEventListener('submit', function(e) {
      e.preventDefault();
      const email = emailInput.value;

      responseMessage.innerHTML = "Processing...";

      fetch(`${API_BASE_URL}/deletion-email/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrftoken
        },
        body: JSON.stringify({ email: email })
      })
      .then(response => {
        if (!response.ok) {
          return response.json().then(err => { throw err; });
        }
        return response.json();
      })
      .then(data => {
        responseMessage.innerHTML = `<p class="success-message">Account deletion request submitted. Please check your email.</p>`;
        form.reset();
      })
      .catch(error => {
          responseMessage.innerHTML = `<p class="error-message">${error?.message}</p>`;
      });
    });
  </script>

</body>
</html>
