# Build Stage
FROM python:3.10-slim-buster as build-python

ENV PYTHONUNBUFFERED=1 \
  PYTHONDONTWRITEBYTECODE=1

WORKDIR /code
COPY ./requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Final Stage
FROM python:3.10-slim-buster

WORKDIR /code
COPY --from=build-python /usr/local /usr/local
COPY --from=build-python /code /code
COPY . .

EXPOSE 8080

CMD ["python", "manage.py", "runserver", "0.0.0.0:8080"]
