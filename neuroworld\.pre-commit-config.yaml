exclude: "^docs/|/migrations/"
default_stages: [commit]

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.2.2
    hooks:
      - id: pyupgrade
        args: [--py310-plus]

  - repo: https://github.com/psf/black
    rev: 22.10.0
    hooks:
      - id: black

  - repo: https://github.com/PyCQA/isort
    rev: 5.11.5
    hooks:
      - id: isort

  - repo: https://github.com/PyCQA/flake8
    rev: 5.0.4
    hooks:
      - id: flake8
        args: ["--config=setup.cfg"]
        additional_dependencies: [flake8-isort]

# sets up .pre-commit-ci.yaml to ensure pre-commit dependencies stay up to date
ci:
  autoupdate_schedule: weekly
  skip: []
  submodules: false
