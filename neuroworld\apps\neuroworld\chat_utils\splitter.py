from langchain.text_splitter import RecursiveCharacterTextSplitter

class Splitter:
  SEPARATORS = ["\n\n", "\n", r"(?<=\.)", " ", ""]

  def get(chunk_size: int, chunk_overlap: int):
    return RecursiveCharacterTextSplitter(
      chunk_size = chunk_size,
      separators = Splitter.SEPARATORS,
      chunk_overlap = chunk_overlap
    )

  def get_text_chunks(text: str, chunk_size: int = 1212, chunk_overlap_percentage: int = 20):
    chunk_overlap = int(chunk_size * (chunk_overlap_percentage / 100))
    return Splitter.get(chunk_size, chunk_overlap).split_text(text)
