import json
import os
import uuid
from django.core.management.base import BaseCommand
from django.db.utils import IntegrityError
from apps.cms.models import Character

class Command(BaseCommand):
    help = "Seed Character records with default values from a JSON file."

    def handle(self, *args, **kwargs):
        file_path = os.path.join(os.path.dirname(__file__), "../../fixtures/characters_data.json")

        try:
            with open(file_path, "r", encoding="utf-8") as file:
                characters_data = json.load(file)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error reading JSON file: {str(e)}"))
            return

        for character_data in characters_data:
            name = character_data["name"]
            role = character_data["role"]
            tone = character_data["tone"]
            background = character_data["background"]
            few_shots = character_data.get("few_shots", [])

            # Prepare few_shots_input in Wagtail StreamField format
            few_shots_input = [
                {
                    "id": str(uuid.uuid4()),
                    "type": "shot",
                    "value": shot
                }
                for shot in few_shots
            ]

            try:
                character_obj, created = Character.objects.get_or_create(
                    name=name,
                    defaults={
                        "role": role,
                        "tone": tone,
                        "background": background,
                        "few_shots": few_shots,
                        "few_shots_input": few_shots_input
                    }
                )
                if not created:
                    character_obj.role = role
                    character_obj.tone = tone
                    character_obj.background = background
                    character_obj.few_shots = few_shots
                    character_obj.few_shots_input = few_shots_input
                    character_obj.save()

                self.stdout.write(self.style.SUCCESS(f"{'Created' if created else 'Updated'} Character: {name}"))

            except IntegrityError as e:
                self.stdout.write(self.style.ERROR(f"Integrity error for Character '{name}': {str(e)}"))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error processing Character '{name}': {str(e)}"))

        self.stdout.write(self.style.SUCCESS("Seeded Character records successfully."))
