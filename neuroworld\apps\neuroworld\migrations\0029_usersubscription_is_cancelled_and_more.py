# Generated by Django 4.2.18 on 2025-05-29 13:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('neuroworld', '0028_rename_usersubscriptions_usersubscription'),
    ]

    operations = [
        migrations.AddField(
            model_name='usersubscription',
            name='is_cancelled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='usersubscription',
            name='is_expired',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='usersubscription',
            name='platform',
            field=models.CharField(blank=True, choices=[('google', 'Google Play'), ('apple', 'Apple App Store')], max_length=20, null=True),
        ),
    ]
