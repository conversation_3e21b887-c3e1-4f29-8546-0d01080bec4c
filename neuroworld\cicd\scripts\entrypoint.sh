#!/bin/bash

# printenv > .env

echo "$ENV_CREDENTIALS" > .env
echo "$FIREBASE_CREDENTIALS" > ./firebase_key.json
echo "$GOOGLE_PLAY_SERVICE_ACCOUNT_CREDENTIALS" > ./google_play_service_account.json
chmod 600 ./firebase_key.json

python3 manage.py migrate
python3 manage.py showmigrations
python3 manage.py collectstatic
python3 manage.py seed_points
python3 manage.py seed_goals
python3 manage.py seed_notifications
python3 manage.py seed_subscriptions
python3 manage.py seed_characters

python3 manage.py seed_badges
celery -A apps.neuroworld worker --loglevel=info &
celery -A apps.neuroworld beat --loglevel=info &

echo "Starting Application..."
exec "$@"
