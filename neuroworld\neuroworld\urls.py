import os
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path
from django.views import defaults as default_views
from rest_framework.authtoken.views import obtain_auth_token

from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework.permissions import IsAdminUser
from django.conf import settings

from apps.neuroworld.utils import serve_apple_site_association, serve_assetlinks, serve_privacy_policy, serve_delete_account_callback, serve_account_deletion

NEUROWORLD_API_URLS = 'apps.neuroworld.urls'
schema_view = get_schema_view(
    openapi.Info(
        title="NeuroWorld API",
        default_version='v1',
        description="NeuroWorld API for the development of NeuroWorld Mobile App",
    ),
    public=True,  # Public access for Swagger documentation.
    permission_classes=(),  # Only admin users can access the schema.
    urlconf=NEUROWORLD_API_URLS,  # Specify the URL configuration for the API.
    patterns=[
        path('neuroworld/api/', include(NEUROWORLD_API_URLS)),
    ]
)

urlpatterns = [
    # Django Admin, use {% url 'admin:index' %}
    path('admin/', admin.site.urls),
    # Your stuff: custom urls includes go here
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),  # URL for Swagger UI.
    path(".well-known/assetlinks.json", serve_assetlinks),
    path(".well-known/apple-app-site-association", serve_apple_site_association),
    path('privacy-policy/', serve_privacy_policy),
    path('account-deletion/', serve_account_deletion),
    path('delete-account-callback/', serve_delete_account_callback),

] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

#Add your own app urls here
urlpatterns += [
    path("neuroworld/api/", include(NEUROWORLD_API_URLS)),
    path("cms/", include("apps.cms.urls")),
]

# API URLS
urlpatterns += [
]

if settings.DEBUG:
    # Static files locally
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

    # This allows the error pages to be debugged during development, just visit
    # these url in browser to see how these error pages look like.
    urlpatterns += [
        path(
            "400/",
            default_views.bad_request,
            kwargs={"exception": Exception("Bad Request!")},
        ),
        path(
            "403/",
            default_views.permission_denied,
            kwargs={"exception": Exception("Permission Denied")},
        ),
        path(
            "404/",
            default_views.page_not_found,
            kwargs={"exception": Exception("Page not Found")},
        ),
        path("500/", default_views.server_error),
    ]
    if "debug_toolbar" in settings.INSTALLED_APPS:
        import debug_toolbar

        urlpatterns = [path("__debug__/", include(debug_toolbar.urls))] + urlpatterns
