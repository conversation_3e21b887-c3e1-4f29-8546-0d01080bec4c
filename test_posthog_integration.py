#!/usr/bin/env python
"""
Simple test script to verify PostHog integration works.
Run this after the dependencies are installed.
"""

import os
import sys
import django
from datetime import datetime

# Add the project directory to Python path
sys.path.append('neuroworld')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neuroworld.settings')

# Setup Django
django.setup()

from apps.neuroworld.analytics import analytics
from apps.neuroworld.models import User, UserProfile

def test_posthog_integration():
    """Test PostHog integration with a sample user."""
    print("🧪 Testing PostHog Integration...")
    
    # Check if PostHog is enabled
    print(f"📊 PostHog Enabled: {analytics.enabled}")
    
    if not analytics.enabled:
        print("❌ PostHog is not enabled. Check your environment variables:")
        print("   - POSTHOG_API_KEY")
        print("   - POSTHOG_HOST")
        print("   - POSTHOG_ENABLED")
        return False
    
    # Try to find an existing user for testing
    test_user = User.objects.first()
    
    if not test_user:
        print("⚠️  No users found in database. Creating a test user...")
        test_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123'
        )
        UserProfile.objects.create(
            user=test_user,
            full_name='Test User',
            gender='M'
        )
        print(f"✅ Created test user: {test_user.email}")
    else:
        print(f"👤 Using existing user: {test_user.email}")
    
    # Test user identification
    print("🔍 Testing user identification...")
    identify_result = analytics.identify_user(test_user)
    print(f"   Result: {'✅ Success' if identify_result else '❌ Failed'}")
    
    # Test event tracking
    print("📝 Testing event tracking...")
    event_result = analytics.track_event(
        user=test_user,
        event_name='Test Event',
        properties={
            'test_property': 'test_value',
            'timestamp': datetime.now().isoformat()
        }
    )
    print(f"   Result: {'✅ Success' if event_result else '❌ Failed'}")
    
    # Test specific tracking methods
    print("🎯 Testing specific tracking methods...")
    
    # Test registration tracking
    reg_result = analytics.track_user_registration(test_user)
    print(f"   Registration tracking: {'✅ Success' if reg_result else '❌ Failed'}")
    
    # Test login tracking
    login_result = analytics.track_user_login(test_user)
    print(f"   Login tracking: {'✅ Success' if login_result else '❌ Failed'}")
    
    # Test profile update tracking
    profile_result = analytics.track_profile_update(test_user, ['full_name', 'email'])
    print(f"   Profile update tracking: {'✅ Success' if profile_result else '❌ Failed'}")
    
    print("\n🎉 PostHog integration test completed!")
    print("📈 Check your PostHog dashboard to see the events.")
    
    return True

if __name__ == '__main__':
    try:
        test_posthog_integration()
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
