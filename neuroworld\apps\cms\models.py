
import os
import tempfile
from django.db import models

import uuid

from apps.neuroworld.interactors.upload_files import upload_to_weaviate
from ..neuroworld.s3_utils import upload_s3_file, generate_presigned_view_url, delete_s3_object
from django.utils.html import format_html

from wagtail.fields import <PERSON><PERSON>ield
from wagtail.blocks import CharBlock

from django import forms
from wagtail.admin.panels import FieldPanel

import logging

logger = logging.getLogger(__name__)

class AIAgentReferenceFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    file_name = models.Char<PERSON>ield("File Name", max_length=255)
    weaviate_id = models.Char<PERSON>ield("Weaviate ID", max_length=255, blank=True, null=True)
    s3_object_key = models.Char<PERSON><PERSON>("S3 Object Key", max_length=255, blank=True, null=True)

    # Temporary file field ONLY for admin file upload
    temp_file = models.FileField("Upload File", upload_to='temp/', blank=True, null=True)

    def __str__(self):
        return self.file_name

    class Meta:
        verbose_name = "AI Agent File"  # Singular
        verbose_name_plural = "AI Agent Files"  # Plural
            
    def save(self, *args, **kwargs):
        if self.temp_file:
            self.file_name = self.temp_file.name
            
            # First, read the file content into memory
            try:
                # Read the entire content at once (for small files)
                file_content = self.temp_file.read()
                
                # Upload to S3 from memory
                from io import BytesIO
                s3_file_obj = BytesIO(file_content)
                s3_file_obj.name = self.file_name
                key, _ = upload_s3_file(s3_file_obj, folder="rag_files")
                self.s3_object_key = key
                
                # Upload to Weaviate from memory
                weaviate_namespace = "cd-1"
                
                # Create a temporary file for Weaviate processing
                with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
                    tmp_file.write(file_content)
                    tmp_file_path = tmp_file.name
                
                try:
                    self.weaviate_id = upload_to_weaviate(
                        file_path=tmp_file_path,
                        file_name=self.file_name,
                        namespace=weaviate_namespace
                    )
                finally:
                    if os.path.exists(tmp_file_path):
                        os.unlink(tmp_file_path)
                
            except ValueError:
                logger.error("Invalid file type. Only PDF, DOCX, and TXT files are supported.")
                return
            except Exception as e:
                logger.error(f"File processing error: {str(e)}")
                raise
            finally:
                # Ensure we close the temp file
                if hasattr(self.temp_file, 'close'):
                    self.temp_file.close()
                self.temp_file = None
        
        super().save(*args, **kwargs)

    def get_presigned_url(self):
        return generate_presigned_view_url(self.s3_object_key)
    
    def delete(self, *args, **kwargs):
        if self.s3_object_key:
            try:
                delete_s3_object(self.s3_object_key)
            except Exception as e:
                # Optional: Log the failure but continue deleting the DB record
                logger.info(f"Failed to delete S3 object: {self.s3_object_key} - {str(e)}")
        super().delete(*args, **kwargs)
    
    @property
    def presigned_url(self):
        if not self.s3_object_key:
            return "No file uploaded"
        return self.get_presigned_url()
    
    def presigned_url_link(self):
        url = self.presigned_url
        if url:
            return format_html('<a href="{}" target="_blank">View File</a>', url)
        return "-"
    
    presigned_url_link.short_description = "File URL"

class Character(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField("Character Name", max_length=255, unique=True)
    role = models.TextField("Role")
    tone = models.TextField("Tone")
    background = models.TextField("Background")
    
    # Final storage: clean list of strings
    few_shots = models.JSONField("Few-Shot Examples", blank=True, default=list)

    # Proxy field: input via Wagtail admin
    few_shots_input = StreamField(
        [("shot", CharBlock(label="Few-Shot Example"))],
        blank=True,
        use_json_field=True,
        verbose_name="Few-Shot Examples Input",
    )

    panels = [
        FieldPanel('name'),
        FieldPanel('role', widget=forms.Textarea(attrs={'rows': 1})),
        FieldPanel('tone', widget=forms.Textarea(attrs={'rows': 5})),
        FieldPanel('background', widget=forms.Textarea(attrs={'rows': 5})),
        FieldPanel('few_shots_input'),
    ]

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        """
        On save: only update few_shots if few_shots_input has data.
        Otherwise, keep the existing few_shots (don't clear it).
        """
        if self.few_shots_input and len(self.few_shots_input) > 0:
            self.few_shots = [block.value for block in self.few_shots_input]
        # If few_shots_input is empty, keep few_shots unchanged.
        super().save(*args, **kwargs)