import logging
from django.conf import settings
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import UserThread, User
from .assistant import Assistant  # Import your Assistant class

logger = logging.getLogger(__name__)

@receiver(post_save, sender=UserThread)
def handle_message_count(sender, instance, **kwargs):
    logger.info(f"User : {instance.user} Signal triggered for UserThread: {instance.thread_id}, message_count: {instance.message_count}")
    # Check if message_count reaches message_count threshold
    if instance.message_count > settings.MESSAGE_COUNT_THRESHOLD:
        logger.info(f"User : {instance.user} Creating new user thread and adding summary")     
        assistant = Assistant(instance.user)
        thread = assistant.create_new_thread_add_summary() #get new thread
        if not thread:
            pass
        else:
            # Save the new thread ID to the user
            user = instance.user
            user.thread_id = thread.id #assign new thread_id
            user.save()
