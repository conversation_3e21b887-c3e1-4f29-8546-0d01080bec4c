from wagtail import hooks
from .views import ai_agent_files_viewset, character_viewset

@hooks.register('register_admin_viewset')
def register_ai_agent_files_viewset():
    return ai_agent_files_viewset

@hooks.register('register_admin_viewset')
def register_character_viewset():
    return character_viewset

@hooks.register('construct_main_menu')
def hide_default_wagtail_items(request, menu_items):
    # Remove Pages, Images, Documents from the sidebar
    menu_items[:] = [
        item for item in menu_items
        if item.name not in ('explorer', 'pages', 'images', 'documents', 'reports', "settings")
    ]
