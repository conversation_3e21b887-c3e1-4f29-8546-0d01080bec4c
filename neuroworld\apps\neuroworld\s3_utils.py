import logging
import uuid
from functools import lru_cache
import boto3
from botocore.exceptions import ClientError, NoCredentialsError, EndpointConnectionError
from boto3.s3.transfer import TransferConfig
from django.conf import settings
import mimetypes


log = logging.getLogger(__name__)

class S3OperationError(Exception):
    """Custom exception for S3 operation errors."""
    pass

@lru_cache(maxsize=1)
def get_s3_client():
    """
    Returns a boto3 S3 client using environment-aware configuration.
    """

    try:
        session = boto3.session.Session()
        if settings.USE_S3_SECRETS:
            return session.client(
                's3',
                region_name=settings.AWS_S3_REGION_NAME,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
        else:
            return session.client('s3', region_name=settings.AWS_S3_REGION_NAME)
    except Exception as e:
        log.exception("[S3] Failed to initialize client.")
        raise S3OperationError("Failed to initialize S3 client") from e


def generate_presigned_view_url(key: str) -> str:
    """
    Generates a presigned URL for viewing an S3 object.
    """
    s3_client = get_s3_client()
    try:
        return s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': settings.AWS_STORAGE_BUCKET_NAME, 'Key': key},
            ExpiresIn=settings.AWS_QUERYSTRING_EXPIRE
        )
    except (ClientError, NoCredentialsError) as e:
        log.exception(f"[S3] Failed to generate presigned URL for key: {key}")
        raise S3OperationError("Unable to generate presigned URL") from e


def delete_s3_object(key: str) -> None:
    """
    Deletes an object from S3. Raises exception if deletion fails.
    """
    if not key:
        raise ValueError("Missing S3 object key for deletion")

    s3_client = get_s3_client()
    try:
        s3_client.delete_object(Bucket=settings.AWS_STORAGE_BUCKET_NAME, Key=key)
    except (ClientError, NoCredentialsError) as e:
        log.exception(f"[S3] Failed to delete object: {key}")
        raise S3OperationError(f"Failed to delete object: {key}") from e


def upload_s3_file(file_obj, folder: str = "profile_pictures") -> tuple[str, str]:
    """
    Uploads a file to S3 and returns the key and presigned URL.
    """
    s3_client = get_s3_client()

    key = f"{folder}/{uuid.uuid4()}"

    content_type, _ = mimetypes.guess_type(file_obj.name)

    try:
        s3_client.upload_fileobj(
            Fileobj=file_obj,
            Bucket=settings.AWS_STORAGE_BUCKET_NAME,
            Key=key,
            ExtraArgs={'ContentType': content_type or 'application/octet-stream'}, 
        )
    except (ClientError, NoCredentialsError, EndpointConnectionError) as e:
        log.exception(f"[S3] Failed to upload file: {key}")
        raise S3OperationError("S3 upload failed") from e

    try:
        presigned_url = generate_presigned_view_url(key)
        return key, presigned_url
    except Exception as e:
        raise S3OperationError("File uploaded but URL generation failed") from e
