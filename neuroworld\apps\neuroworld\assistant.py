import json
import time
import logging
from typing import List
from django.conf import settings
from openai import APIConnectionError, APIError, BadRequestError, OpenAI, OpenAIError, RateLimitError
from .models import NeuroChat, User, UserThread
from openai.types.beta.threads import Message
from .constants import ASSISTANT_INSTRUCTIONS
from .serializers import NeuroChatSerializer
from .utils import convert_uuid_to_str
from .assistant_functions import delete_goal, get_goal_version, get_goals, get_points_information, get_user_goals, get_user_profile, get_user_streaks_and_points, introduce_character, finalize_goal
from openai.types.beta.threads import RequiredActionFunctionToolCall


logger = logging.getLogger(__name__)

SUMMARY_PROMPT = "Below is a summary of the latest chat. Use this summary as context to seamlessly continue the conversation. Remember to not reply to this and no function call should be made. Just feed it to the assistant. \n\n"
SUMMARY_MESSAGE_LIMIT = 30
SUMMARY_MODEL='gpt-4o-mini'

def parse_messages(data):
    parsed_messages = []
    for message in data.data:
        role = message.role
        content_list = message.content
        for content in content_list:
            if content.type == 'text':
                text_value = content.text.value
                parsed_messages.append((role, text_value))
    return parsed_messages

class Assistant:
    def __init__(self, user: User, message: str = None):
        self.client = OpenAI(api_key=settings.OPEN_AI_KEY)
        self.user = user
        self.user_message = message
        self.assistant_id = settings.ASSISTANT_ID

    def get_or_create_assistant(self):
        create_new_assistant = settings.CREATE_NEW_ASSISTANT
        if self.assistant_id:
            return self._retrieve_assistant()
        elif create_new_assistant:
            return self._create_assistant()
        else:
            raise Exception("Assistant ID not found and CREATE_NEW_ASSISTANT is not set to True")

    def _retrieve_assistant(self):
        try:
            return self.client.beta.assistants.retrieve(assistant_id=self.assistant_id)
        except (APIError, APIConnectionError, RateLimitError) as e:
            logger.warning(f"OpenAI API error: {e}")
            raise e

    def _create_assistant(self):
        try:
            assistant = self.client.beta.assistants.create(
                name="Brain Health Coach",
                instructions=self._get_assistant_instructions(),
                tools=[{"type": "file_search"}],
                model="gpt-4o-mini",
            )
            return self.client.beta.assistants.retrieve(assistant_id=assistant.id)
        except (APIError, APIConnectionError, RateLimitError) as e:
            logger.warning(f"OpenAI API error: {e}")
            raise e

    def _get_assistant_instructions(self):
        return ASSISTANT_INSTRUCTIONS

    def get_or_create_thread(self, new: bool = False):
        try:
            if new or not self.user.thread_id:
                logger.info("Creating a new thread")
                thread = self.client.beta.threads.create()
                self.user.thread_id = thread.id
                self.user.save()

                UserThread.objects.create(
                        user=self.user,
                        thread_id=thread.id,
                        message_count=0  # Reset message count for the new thread
                    )            
            else:
                logger.info("Retrieving existing thread")
                thread = self.client.beta.threads.retrieve(self.user.thread_id)
            return thread
        except (APIError, APIConnectionError, RateLimitError) as e:
            logger.warning(f"OpenAI API error: {e}")
            raise e

    def create_message(self, thread_id, message, role='user'):
        try:
            return self.client.beta.threads.messages.create(
                thread_id=thread_id,
                role=role,
                content=message,
            )

        except BadRequestError as e:
            logger.warning(f"Bad request error: {e}")
            run_id = parse_run_id_from_exception(e)
            if run_id:
                logger.warning(f"Run ID from exception: {run_id}")
                # Attempt to cancel the run if a run ID is found
                self.cancel_run(thread_id, run_id)

                return self.client.beta.threads.messages.create(
                thread_id=thread_id,
                role=role,
                content=message,
            )
            else:
                logger.warning("No run ID found in exception")
                raise e
        except (APIError, APIConnectionError, RateLimitError) as e:
            logger.warning(f"OpenAI API error: {e}")
            raise e           

    def run(self, thread_id):
        try:
            run = self.client.beta.threads.runs.create(
                thread_id=self.get_or_create_thread().id,
                assistant_id=self.get_or_create_assistant().id,
            )
            return self.wait_on_run(run, thread_id)
        except (APIError, APIConnectionError, RateLimitError) as e:
            logger.warning(f"OpenAI API error: {e}")
            raise e

    def wait_on_run(self, run, thread_id):
        while run.status in ["queued", "in_progress"]:
            run = self.client.beta.threads.runs.retrieve(
                thread_id=thread_id,
                run_id=run.id,
            )
            time.sleep(0.5)
        return run

    def get_messages(self, thread_id, limit=None):
        try:
            messages = self.client.beta.threads.messages.list(thread_id=thread_id, order="desc")
            return parse_messages(messages)
        except (APIError, APIConnectionError, RateLimitError) as e:
            logger.warning(f"OpenAI API error: {e}")
            raise e

    def get_thread_stream_async(self, thread_id):
        try:
            assistant_message = ""
            with self.client.beta.threads.runs.stream(
                thread_id=thread_id,
                assistant_id=self.assistant_id,
            ) as stream:
                for result in self.handle_stream_async(thread_id, stream):
                    if result.event == "thread.message.completed":
                        text_content = result.data.content[0].text
                        assistant_message = text_content.value if hasattr(text_content, 'value') else text_content
                        assistant_message = self.save_message_to_db(self.user_message, assistant_message, thread_id)
                        
                        try:
                            assistant_message_dict = json.loads(assistant_message["message"])
                            assistant_message["message"] = assistant_message_dict
                        except (json.JSONDecodeError, KeyError, TypeError):
                            # fall back if parsing fails
                            pass

                        chunk = json.dumps({"chunk": assistant_message, "last_message": True})
                        yield chunk

                    elif result.event == "thread.message.delta":
                        data = {"chunk": result.data.delta.content[0].text.value, "last_message": False}
                        chunk = json.dumps(data)
                        yield chunk

                stream.until_done()    

        except (APIError, APIConnectionError, RateLimitError) as e:
            logger.warning(f"OpenAI API error: {e}")
            raise e
        except Exception as e:
            logger.warning(f"error streaming chat: {e}")
            raise e

    def handle_stream_async(self, thread_id, stream):
        try:
            for event in stream:
                if event.event == 'thread.message.delta' and event.data.delta.content:
                    yield event
                elif event.event == 'thread.run.requires_action':
                    run_id = event.data.id
                    tool_outputs = self.handle_requires_action(event.data.required_action.submit_tool_outputs.tool_calls)

                    with self.client.beta.threads.runs.submit_tool_outputs_stream(
                        thread_id=thread_id,
                        run_id=run_id,
                        tool_outputs=tool_outputs
                    ) as new_stream:
                        for result in self.handle_stream_async(thread_id, new_stream):
                            yield result
                elif event.event == "thread.message.completed":
                    if event.data.content[0].text.annotations:
                        event.data.content[0].text = self.remove_citations(event.data)
                        yield event
                    yield event    
                elif event.event == 'thread.run.completed':
                    break
        except (APIError, APIConnectionError, RateLimitError) as e:
            logger.warning(f"OpenAI API error: {e}")
            raise e

    def process_annotations(self, message: Message):
        message_content = message.content[0].text
        annotations = message_content.annotations
        citations = []
        for index, annotation in enumerate(annotations):
            message_content.value = message_content.value.replace(annotation.text, f' [{index+1}]')
            if (file_citation := getattr(annotation, 'file_citation', None)):
                cited_file = self.client.files.retrieve(file_citation.file_id)
                citations.append(f'[{index+1}] {cited_file.filename}')
        message_content.value += '\n\n' + '\n\n'.join(citations)
        return message_content.value

    def remove_citations(self, message: Message):
        message_content = message.content[0].text
        annotations = message_content.annotations
        for annotation in annotations:
            message_content.value = message_content.value.replace(annotation.text, '')
        return message_content.value
    
    def save_message_to_db(self, user_message, assistant_message, thread_id):
        NeuroChat.objects.create(user=self.user, message=user_message, role="user")
        assistant_reply_object = NeuroChat.objects.create(user=self.user, message=assistant_message, role="assistant")

        user_thread, created = UserThread.objects.get_or_create(user=self.user, thread_id=thread_id)

        if created:
            user_thread.message_count = 2
            user_thread.save()
        else:
            user_thread.message_count += 2
            user_thread.save()

        # Serialize the assistant reply and return it
        assistant_reply_object = NeuroChatSerializer(assistant_reply_object).data
        assistant_reply_object = convert_uuid_to_str(assistant_reply_object)

        return assistant_reply_object
    
    def get_messages_from_db(self, max_messages=20):
    # Fetch messages limited by the message_limit, ordered by date descending
        messages = NeuroChat.objects.filter(user=self.user).order_by('-date')[:max_messages]
        if not messages:
            return None
        
        messages = reversed(messages)
        # Structure messages into the required dictionary format
        structured_messages = [{message.get_role_display(): message.message} for message in messages]

        return structured_messages
    
    def summarize_conversation_with_openai(self, message_limit):
        """
        Summarizes a conversation using OpenAI's GPT-40-mini
        Args:
            message_limit (int): The maximum number of messages to fetch and summarize.

        Returns:
            str: A concise and descriptive summary of the conversation.
        """
        # Fetch messages from the database
        messages = self.get_messages_from_db(message_limit)

        if not messages:
            return "No messages available to summarize."

        conversation_context = (
            """
This is a conversation between a brain health coach and a managee focused on personalized guidance. The task is to summarize the conversation in a concise and clear manner, following a User and Coach conversational structure.

Summary Instructions:

1. The summary should clearly present the dialogue by outlining each interaction, formatted as:
'User asked [question/concern]' and the 'Coach responded with [advice/information].'

2. Ensure that the summary is easy to follow, covering the key topics discussed without unnecessary detail.

3. Focus on what the User asked and how the Coach responded, capturing the essence of the advice or information provided.

4.The summary should be concise, highlighting main points and relevant guidance.

5. If Coach has provided specific activity, diet or any detail we should incorporate it as is.

6. If coach provides plan or teaching guides , make sure to include that detail day-wise or step-wise.

7. Stay under 300 words.

8. Important! make sure to mention where user talked or requested anything other brain health and what coach said , and appreciate it.

Conversation to summarize (provided below in triple backticks):

```
        """
        )

        # Format messages for inclusion in the prompt
        formatted_messages = "\n\n".join(
    [f"{role}: {message[:500]}" for message_dict in messages for role, message in message_dict.items()]
)
        # Construct the complete prompt
        prompt = f"{conversation_context}\n\n{formatted_messages}\n ```"
        try:
            # Make API call to OpenAI


            response = self.client.chat.completions.create(
                    model=SUMMARY_MODEL,
                    messages=[
                    {"role": "system", "content": "You are best at summarizing"},
                    {"role": "user", "content": prompt}
                ]
            )
            # Extract and return the summary from the response
            summary = response.choices[0].message.content
            return summary
        except OpenAIError as e:
            return f"Error generating summary: {str(e)}"

    def create_new_thread_add_summary(self, message_limit=SUMMARY_MESSAGE_LIMIT, timeout=5):
        try:
            summary = self.summarize_conversation_with_openai(message_limit)
            summary = SUMMARY_PROMPT + summary #add summary prompt
            thread = self.get_or_create_thread(new=True)
            self.create_message(thread.id, summary, role='assistant')
            run = self.run(thread_id=thread.id)

            start_time = time.time()

            while run.status != "completed":
                time.sleep(1)  # Polling interval
                run = self.client.beta.threads.runs.retrieve(
                thread_id=thread.id,
                run_id=run.id
            )
                # Handle timeouts
                if time.time() - start_time > timeout:
                    logger.warning(f"Run {run.id} exceeded timeout of {timeout} seconds. Cancelling run.")
                    run = self.cancel_run(thread.id, run.id)  # Cancel the run
                    break

                # Handle failed or cancelled runs
                if run.status in ["failed", "cancelled"]:
                    logger.warning(f"Run {run.id} ended with status: {run.status}")
                    break

            # Step 6: Log completion
            if run.status == "completed":
                logger.info(f"Run {run.id} completed successfully.")
            else:
                logger.warning(f"Run {run.id} ended with status: {run.status}")

            return thread


        except Exception as e:
            logger.warning(f"Unable to create new thread : {e}")
            return None

    def handle_requires_action(self, tool_calls: List[RequiredActionFunctionToolCall]):
        tool_outputs = []
        for tool in tool_calls:
            match tool.function.name:
                case "get_goals":
                    logger.info(f"User: {self.user}- Get all goals function call")
                    arguments = json.loads(tool.function.arguments)  # Change from tool.args to tool.arguments if necessary
                    category = arguments.get("category")                    
                    tool_outputs.append({
                        "tool_call_id": tool.id,
                        "output": json.dumps(get_goals(user=self.user ,category=category))
                    })
                case "get_user_goals":
                    logger.info(f"User: {self.user}- Get user goals function call")
                    tool_outputs.append({
                        "tool_call_id": tool.id,
                        "output": json.dumps(get_user_goals(self.user))
                    })
                case "get_goal_version":
                    logger.info(f"User: {self.user}- Get goal version function call")
                    arguments = json.loads(tool.function.arguments)  # Change from tool.args to tool.arguments if necessary
                    goal_id = arguments.get("goal_id")
                    tool_outputs.append({
                        "tool_call_id": tool.id,
                        "output": json.dumps(get_goal_version(goal_id))
                    })                        
                case "finalize_goal":
                    logger.info(f"User: {self.user}- Select goal function call")
                    arguments = json.loads(tool.function.arguments)  # Change from tool.args to tool.arguments if necessary
                    goal_version_id = arguments.get("goal_id")
                    start_date = arguments.get("start_date")
                    end_date = arguments.get("end_date")
                    week_target = arguments.get("week_target")
                    tool_outputs.append({
                        "tool_call_id": tool.id,
                        "output": json.dumps(finalize_goal(user=self.user, goal_version_id=goal_version_id, start_date=start_date, end_date=end_date, week_target=week_target))
                    })
                case "delete_goal":
                    logger.info(f"User: {self.user}- Delete goal function call")
                    arguments = tool.function.arguments  
                    arguments_dict = json.loads(arguments)  
                    
                    goal_id = arguments_dict.get("goal_id")
                    
                    # Call the delete_goal function and append the response
                    tool_outputs.append({
                        "tool_call_id": tool.id,
                        "output": json.dumps(delete_goal(self.user, goal_id))
                    })
                case "introduce_character":
                    logger.info(f"User: {self.user}- Introduce character function call")
                    arguments = tool.function.arguments  
                    arguments_dict = json.loads(arguments)  
                    character_name = arguments_dict.get("character_name")
                    response = json.dumps(introduce_character(character_name))
                    tool_outputs.append({
                        "tool_call_id": tool.id,
                        "output": response
                    })
                case "get_user_profile":
                    logger.info(f"User: {self.user}- Get user profile function call")
                    tool_outputs.append({
                        "tool_call_id": tool.id,
                        "output": json.dumps(get_user_profile(self.user))
                    })
                case "get_user_streaks_and_points":
                    logger.info(f"User: {self.user}- Get user streaks and points function call")
                    tool_outputs.append({
                        "tool_call_id": tool.id,
                        "output": json.dumps((get_user_streaks_and_points(self.user)))
                    })
                case "get_points_info":
                    logger.info(f"User: {self.user}- Get points function call")
                    tool_outputs.append({
                        "tool_call_id": tool.id,
                        "output": json.dumps((get_points_information()))
                    })                            
        return tool_outputs

    def cancel_run(self, thread_id, run_id):
        """
        Cancel a run forcefully.
        """
        try:
            run = self.client.beta.threads.runs.cancel(
                thread_id=thread_id,
                run_id=run_id
            )
            return run
        except Exception as e:
            logger.error(f"Error cancelling run: {e}")
            raise

import re

def parse_run_id_from_exception(e):
    error_message = str(e)
    match = re.search(r'run_[a-zA-Z0-9]+', error_message)
    if match:
        return match.group(0)
    return None