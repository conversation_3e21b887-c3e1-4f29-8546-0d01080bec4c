# Generated by Django 4.2.18 on 2025-05-26 11:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('neuroworld', '0026_userprofile_inactivity_reminder_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tier', models.CharField(choices=[('free', 'Free'), ('habits', 'Habits'), ('summit', 'Summit'), ('academy', 'Academy'), ('clinical', 'Clinical')], max_length=20)),
                ('name', models.CharField(max_length=50)),
                ('billing_period', models.CharField(blank=True, choices=[('monthly', 'Monthly'), ('yearly', 'Yearly'), ('quarterly', 'Quarterly')], max_length=20, null=True)),
                ('google_product_id', models.Char<PERSON>ield(blank=True, max_length=255, null=True, unique=True)),
                ('apple_product_id', models.CharField(blank=True, max_length=255, null=True, unique=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('duration_days', models.IntegerField(blank=True, null=True)),
            ],
        ),
        migrations.AlterField(
            model_name='user',
            name='plan',
            field=models.CharField(choices=[('free', 'Free'), ('habits', 'Habits'), ('summit', 'Summit'), ('academy', 'Academy'), ('clinical', 'Clinical')], default='free', max_length=50),
        ),
        migrations.CreateModel(
            name='UserSubscriptions',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('purchase_token', models.CharField(max_length=512, unique=True)),
                ('status', models.CharField(default='active', max_length=100)),
                ('start_time', models.DateTimeField(blank=True, null=True)),
                ('expiry_time', models.DateTimeField(blank=True, null=True)),
                ('is_auto_renewing', models.BooleanField(default=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('plan', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='neuroworld.subscriptionplan')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
