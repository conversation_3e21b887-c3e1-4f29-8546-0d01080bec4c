"""
Base settings to build other settings files upon.
"""
from datetime import <PERSON><PERSON><PERSON>
import json
from pathlib import Path

import environ

from celery.schedules import crontab


ROOT_DIR = Path(__file__).resolve(strict=True).parent.parent
# neuroworld/
APPS_DIR = ROOT_DIR / "apps"
env = environ.Env()

READ_DOT_ENV_FILE = env.bool("DJANGO_READ_DOT_ENV_FILE", default=True)
if READ_DOT_ENV_FILE:
    # OS environment variables take precedence over variables from .env
    env.read_env(str(ROOT_DIR / ".env"))

# GENERAL
# ------------------------------------------------------------------------------
ADMIN_URL = env("DJANGO_ADMIN_URL", default="some-admin-url")
SECRET_KEY = env("DJANGO_SECRET_KEY", default="some-secret-key")
# https://docs.djangoproject.com/en/dev/ref/settings/#allowed-hosts
ALLOWED_HOSTS = env.list("DJANGO_ALLOWED_HOSTS", default=["example.com"])
# https://docs.djangoproject.com/en/dev/ref/settings/#debug
CORS_ORIGIN_ALLOW_ALL = env('CORS_ORIGIN_ALLOW_ALL', default=False)
CSRF_TRUSTED_ORIGINS = env.list('CSRF_TRUSTED_ORIGINS', default=[])
CORS_ORIGIN_WHITELIST = env.list(
        "CORS_ORIGIN_WHITELIST", default=["http://localhost:3000"]
    )
DEBUG = env.bool("DJANGO_DEBUG", False)
# Local time zone. Choices are
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# though not all of them may be available with every OS.
# In Windows, this must be set to your system time zone.
TIME_ZONE = "UTC"
# https://docs.djangoproject.com/en/dev/ref/settings/#language-code
LANGUAGE_CODE = "en-us"
# https://docs.djangoproject.com/en/dev/ref/settings/#site-id
SITE_ID = 1
# https://docs.djangoproject.com/en/dev/ref/settings/#use-i18n
USE_I18N = True
# https://docs.djangoproject.com/en/dev/ref/settings/#use-l10n
USE_L10N = True
# https://docs.djangoproject.com/en/dev/ref/settings/#use-tz
USE_TZ = True
# https://docs.djangoproject.com/en/dev/ref/settings/#locale-paths
LOCALE_PATHS = [str(ROOT_DIR / "locale")]

# DATABASES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#databases

DATABASES = {
    "default": env.db(
        "DATABASE_URL",
        default="postgres://postgres:postgres@127.0.0.1:5432/neuroworld",
    ),
}
DATABASES["default"]["ATOMIC_REQUESTS"] = True
# https://docs.djangoproject.com/en/stable/ref/settings/#std:setting-DEFAULT_AUTO_FIELD
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# URLS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#root-urlconf
ROOT_URLCONF = "neuroworld.urls"
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# https://docs.djangoproject.com/en/dev/ref/settings/#wsgi-application
WSGI_APPLICATION = "neuroworld.wsgi.application"

# APPS
# ------------------------------------------------------------------------------
DJANGO_APPS = [
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.sites",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.admin",
    "django.forms",
    "django_celery_beat",
]
THIRD_PARTY_APPS = [
    "rest_framework",
    "rest_framework.authtoken",
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    "corsheaders",
    "drf_spectacular",
    'drf_yasg',
    'storages'
]

LOCAL_APPS = [
    # Your stuff: custom apps go here
    "apps.neuroworld",
    "apps.cms",
]

WAGTAIL_APPS = [
    'wagtail.contrib.forms',
    'wagtail.contrib.redirects',
    'wagtail.embeds',
    'wagtail.sites',
    'wagtail.users',
    'wagtail.snippets',
    'wagtail.documents',
    'wagtail.images',
    'wagtail.search',
    'wagtail.admin',
    'wagtail',

    'modelcluster',
    'taggit'
]

# https://docs.djangoproject.com/en/dev/ref/settings/#installed-apps
INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS + WAGTAIL_APPS

AUTH_USER_MODEL = 'neuroworld.User'

SIMPLE_JWT = {
    # temporary
    'ACCESS_TOKEN_LIFETIME': timedelta(seconds=60*60*24*30),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=30),
}


# GOOGLE_CLIENT_ID=env.str('GOOGLE_CLIENT_ID',default='some-google-client-id')
# GOOGLE_CLIENT_SECRET=env.str('GOOGLE_CLIENT_SECRET',default='some-google-client-secret')

AUTH_APPLE_KEY_ID = env("AUTH_APPLE_KEY_ID", default="some-key-id")
AUTH_APPLE_TEAM_ID = env("AUTH_APPLE_TEAM_ID", default="some-team-id")
AUTH_APPLE_PRIVATE_KEY = env("AUTH_APPLE_PRIVATE_KEY", default="some-private-key")
AUTH_APPLE_CLIENT_ID = env("AUTH_APPLE_CLIENT_ID", default="some-client-id")
AUTH_APPLE_APP_ID = env("AUTH_APPLE_APP_ID", default="some-app-id")
ACCESS_TOKEN_URL = env("ACCESS_TOKEN_URL", default="some-access-token-url")
AUTH_APPLE_ALGORITHM = env("AUTH_APPLE_ALGORITHM", default="some-algorithm")


# MIGRATIONS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#migration-modules
MIGRATION_MODULES = {}

# PASSWORDS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#password-hashers
PASSWORD_HASHERS = [
    # https://docs.djangoproject.com/en/dev/topics/auth/passwords/#using-argon2-with-django
    "django.contrib.auth.hashers.Argon2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher",
    "django.contrib.auth.hashers.BCryptSHA256PasswordHasher",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator"
    },
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
]

# MIDDLEWARE
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#middleware
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    'whitenoise.middleware.WhiteNoiseMiddleware',
    "corsheaders.middleware.CorsMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    'wagtail.contrib.redirects.middleware.RedirectMiddleware'
]


# STATIC
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#static-root
STATIC_ROOT = str(ROOT_DIR / "staticfiles")
# https://docs.djangoproject.com/en/dev/ref/settings/#static-url
STATIC_URL = "/static/"
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#std:setting-STATICFILES_DIRS
STATICFILES_DIRS = [
    str(APPS_DIR / "neuroworld/static"),
    ]
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#staticfiles-finders
STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
]

STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
# MEDIA
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#media-root
MEDIA_ROOT = str(APPS_DIR / "media")
# https://docs.djangoproject.com/en/dev/ref/settings/#media-url
MEDIA_URL = "/media/"

# TEMPLATES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#templates
TEMPLATES = [
    {
        # https://docs.djangoproject.com/en/dev/ref/settings/#std:setting-TEMPLATES-BACKEND
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        # https://docs.djangoproject.com/en/dev/ref/settings/#dirs
        "DIRS": [str(APPS_DIR / "neuroworld/templates")],
        # https://docs.djangoproject.com/en/dev/ref/settings/#app-dirs
        "APP_DIRS": True,
        "OPTIONS": {
            # https://docs.djangoproject.com/en/dev/ref/settings/#template-context-processors
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.tz",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    }
]

# LOGGING
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#logging
# See https://docs.djangoproject.com/en/dev/topics/logging for
# more details on how to customize your logging configuration.
DISABLE_EXISTING_LOGGERS = env.bool('DISABLE_EXISTING_LOGGERS', default=False)
PROD_LOGGERS={}
if DISABLE_EXISTING_LOGGERS: #TODO change this so that all this info is obtained through environment variables
    PROD_LOGGERS = {
        "django.db.backends": {
            "level": "ERROR",
            "handlers": ["console"],
            "propagate": False,
        },
        # Errors logged by the SDK itself
        "sentry_sdk": {"level": "ERROR", "handlers": ["console"], "propagate": False},
        "django.security.DisallowedHost": {
            "level": "ERROR",
            "handlers": ["console"],
            "propagate": False,
        },
    }


LOGGING = {
    "version": 1,
    "disable_existing_loggers": DISABLE_EXISTING_LOGGERS,
    "formatters": {
        "verbose": {
            "format": "%(levelname)s %(asctime)s %(module)s "
            "%(process)d %(thread)d %(message)s"
        }
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        }
    },
    "root": {"level": "INFO", "handlers": ["console"]},
    "loggers": PROD_LOGGERS
}

# django-rest-framework
# -------------------------------------------------------------------------------
# django-rest-framework - https://www.django-rest-framework.org/api-guide/settings/
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 25,  # Default page size if limit is not specified in query
}


LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'django_info.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': True,
        },
        'your_app_name': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

ASSISTANT_ID = env('ASSISTANT_ID')
CREATE_NEW_ASSISTANT = env('CREATE_NEW_ASSISTANT', default=False, cast=bool)
OPEN_AI_KEY = env('OPEN_AI_KEY')

# EMAIL SETTINGS
USE_AWS_SES = env('USE_AWS_SES', default=False, cast=bool)
EMAIL_BACKEND =  env('EMAIL_BACKEND')
EMAIL_HOST = env('EMAIL_HOST') 
EMAIL_PORT = env('EMAIL_PORT') 
DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL')
SUPPORT_MAIL_RECIPIENT = env('SUPPORT_MAIL_RECIPIENT', default='')

if USE_AWS_SES:
    EMAIL_USE_TLS = env('EMAIL_USE_TLS')
else:
    EMAIL_HOST_USER = env("EMAIL_HOST_USER", default="")
    EMAIL_HOST_PASSWORD = env("EMAIL_HOST_PASSWORD", default="")
    EMAIL_USE_SSL = env('EMAIL_USE_SSL', default=True)

RESET_PASSWORD_DEEP_LINK = env('RESET_PASSWORD_DEEP_LINK', default='http://localhost:3000')

# AWS S3 Storage
# ---------------------------------------------------------------------

DEFAULT_FILE_STORAGE = env('DEFAULT_FILE_STORAGE', default='')
USE_S3_SECRETS = env("USE_S3_SECRETS", default=False)

if USE_S3_SECRETS:
    AWS_ACCESS_KEY_ID = env('AWS_ACCESS_KEY_ID', default='')
    AWS_SECRET_ACCESS_KEY = env('AWS_SECRET_ACCESS_KEY', default='')

AWS_STORAGE_BUCKET_NAME = env('AWS_STORAGE_BUCKET_NAME', default='')
AWS_S3_REGION_NAME = env('AWS_S3_REGION_NAME', default='')
AWS_QUERYSTRING_EXPIRE = env.int('AWS_QUERYSTRING_EXPIRE', default=3600)

GOOGLE_APPLICATION_CREDENTIALS = ROOT_DIR / env('GOOGLE_APPLICATION_CREDENTIALS', default='')


MESSAGE_COUNT_THRESHOLD = env.int('MESSAGE_COUNT_THRESHOLD', default=50)


# ----------------------------------------------------------------------------
# Celery settings
# ----------------------------------------------------------------------------
FCM_BATCH_SIZE=500
STALE_FCM_TOKEN_EXPIRATION_DAYS = 270

ENVIRONMENT_NAME=env('ENVIRONMENT_NAME', default='dev')

NOTIFICATION_SCHEDULE = {
    'morning_engagements': {
        'start_hour': 7,
        'end_hour': 9,
        'type': 'morning_engagements',
        'sqs_queue': f'neuro-morning-notifications-{ENVIRONMENT_NAME}',
        'select_multiple': True,
    },
    'midday_support': {
        'start_hour': 11,
        'end_hour': 14,
        'type': 'midday_support',
        'sqs_queue': f'neuro-midday-notifications-{ENVIRONMENT_NAME}',
        'select_multiple': True,
    },
    'evening_wrap_ups': {
        'start_hour': 18,
        'end_hour': 20,
        'type': 'evening_wrap_ups',
        'sqs_queue': f'neuro-evening-notifications-{ENVIRONMENT_NAME}',
        'select_multiple': True,
    },
    'monthly_engagement': {
        'start_hour': 11,
        'end_hour': 14,
        'sqs_queue': f'neuro-monthly-engagement-notifications-{ENVIRONMENT_NAME}',
        'type': 'monthly_engagements',
        'monthly': True,
    },
    'weekly_monday_engagements': {
        'types': 'weekly_engagements',
        'sub_type': 'new_week_goals',
        'week_day': 0,
        'start_hour': 6,
        'end_hour': 9,
        'sqs_queue': f'neuro-weekly-engagements-monday-notifications-{ENVIRONMENT_NAME}'
    },
    'weekly_wednesday_checkin': {
        'type': 'weekly_engagements',
        'sub_type': 'midweek_checkin',
        'week_day': 2,
        'start_hour': 11,
        'end_hour': 14,
        'sqs_queue': f'neuro-weekly-engagements-wednesday-notifications-{ENVIRONMENT_NAME}'
    },
    'weekly_sunday_rest': {
        'types': 'weekly_engagements',
        'sub_type': 'rest_reminder',
        'week_day': 6,
        'start_hour': 11,
        'end_hour': 14,
        'sqs_queue': f'neuro-weekly-engagements-sunday-notifications-{ENVIRONMENT_NAME}'
    },
    'profile_completion_reminders': {
        'start_hour': 9,
        'sqs_queue': f'neuro-profile-completion-notifications-{ENVIRONMENT_NAME}',
    },
    'missed_streak_reminders': {
        'start_hour': 14,
        'minute': 30,
        'sqs_queue': f'neuro-missed-streak-notifications-{ENVIRONMENT_NAME}',
    },
    'checkin_reminders': {
        'start_hour': 6,
        'end_hour': 8,
        'sqs_queue': f'neuro-checkin-reminder-notifications-{ENVIRONMENT_NAME}',
    },
    'weekly_progress_summary': {
        'start_hour': 19,
        'week_day': 6,
        'sqs_queue': f'neuro-weekly-progress-summary-notifications-{ENVIRONMENT_NAME}',
    },
    're_engagement_reminders': {
        'start_hour': 15,
        'end_hour': 17,
        'sqs_queue': f'neuro-re-engagement-reminders-{ENVIRONMENT_NAME}',
        'select_multiple': True,
    }
}

AWS_REGION = env('AWS_SQS_REGION', default='us-east-1')

CELERY_BROKER_URL = 'sqs://'

CELERY_BROKER_TRANSPORT_OPTIONS = {
    'region': AWS_REGION,  # or your region
    'visibility_timeout': 3600,
    'polling_interval': 5,
}

CELERY_BEAT_SCHEDULE = {
    'schedule-morning': {
        'task': 'apps.neuroworld.tasks.schedule_morning',
        'schedule': crontab(minute='*/30'), # Every 30 minutes
    },
    'schedule-midday': {
        'task': 'apps.neuroworld.tasks.schedule_midday',
        'schedule': crontab(minute='*/30'), # Every 30 minutes
    },
    'schedule-evening': {
        'task': 'apps.neuroworld.tasks.schedule_evening',
        'schedule': crontab(minute='*/30'), # Every 30 minutes
    },
    'schedule-profile-completion-reminder': {
        'task': 'apps.neuroworld.tasks.schedule_profile_completion_reminder',
        'schedule': crontab(minute='*/10'), # Every 10 minutes
    },
    'schedule-missed-streak-reminders': {
        'task': 'apps.neuroworld.tasks.schedule_streak_reminders',
        'schedule': crontab(minute='*/10'), # Every 10 minutes
    },
    'schedule-checkin-reminders': {
        'task': 'apps.neuroworld.tasks.schedule_checkin_reminders',
        'schedule': crontab(minute="*/30"), # Every 30 minutes
    },
    'schedule-monthly-engagements': {
        'task': 'apps.neuroworld.tasks.schedule_monthly_engagement',
        'schedule': crontab(minute="*/30"), # Every 30 minutes
    },
    'schedule-weekly-progress-summary': {
        'task': 'apps.neuroworld.tasks.schedule_weekly_progress_notifications',
        'schedule': crontab(minute='*/30'), # Every 30 minutes
    },
    'schedule-re-engagement-reminders': {
        'task': 'apps.neuroworld.tasks.schedule_re_engagement_reminders',
        'schedule': crontab(minute='*/30'), # Every 30 minutes
    },
    'process-sqs-queues':{
        'task': 'apps.neuroworld.tasks.process_sqs_queues',
        'schedule': crontab(minute='*/10'),  # Every 10 minutes
    },
    'delete-stale-fcm-tokens': {
        'task': 'apps.neuroworld.tasks.delete_stale_fcm_tokens',
        'schedule': crontab(minute=0, hour=0),  # Every day at midnight
    }
}

# ----------------------------------- ChatBot Settings -----------------------------------
OPENAI_MODEL_NAME = env('OPENAI_MODEL_NAME', default='gpt-4o')

WEAVIATE_URL = env('WEAVIATE_URL', default='http://localhost:8080')
WEAVIATE_API_KEY = env('WEAVIATE_API_KEY', default='')
WEAVIATE_INDEX_NAME = env('WEAVIATE_INDEX_NAME', default='Neurobot')

# ---------------------------------Wagtail settings -----------------------------------
WAGTAIL_SITE_NAME = 'NeuroWorld CMS'
WAGTAILDOCS_EXTENSIONS = [ 'docx', 'pdf']

GOOGLE_PLAY_SERVICE_ACCOUNT_FILE = ROOT_DIR / 'google_play_service_account.json'

WAGTAILADMIN_BASE_URL = env('WAGTAILADMIN_BASE_URL', default='')