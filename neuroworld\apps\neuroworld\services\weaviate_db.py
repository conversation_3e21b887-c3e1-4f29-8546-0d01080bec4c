import os
from django.conf import settings
import weaviate
from langchain.vectorstores import Weaviate as LangchainWeaviate

class WeaviateDB:
    client = weaviate.Client(
        url=settings.WEAVIATE_URL,
        auth_client_secret=weaviate.AuthApiKey(settings.WEAVIATE_API_KEY),
        additional_headers={"X-OpenAI-Api-Key": settings.OPEN_AI_KEY},
        timeout_config=(10, 60),  # (connect_timeout, read_timeout)
    )
    
    # client = weaviate.Client(
    #     url='http://localhost:8080',
    #     additional_headers={}
    # )

    @classmethod
    def store(cls, namespace, text_chunks, embeddings, file_name=''):
        """Store text chunks in Weaviate using the embedding function"""
        metadatas = [{'file_name': file_name} for _ in text_chunks]
        
        # Create vectorstore with the embedding function
        vectorstore = cls.vectorstore(embeddings=embeddings)
        
        # This will automatically use embed_documents on the texts
        vectorstore.add_texts(texts=text_chunks, metadatas=metadatas)
        return namespace

    def get_similar_docs(
        user_query: str,
        namespace: str,
        no_of_docs: int,
        embeddings: list,
        similarity_score: float,
    ):
        vectorstore = WeaviateDB.vectorstore(embeddings=embeddings)
        query_embedding = vectorstore._embedding.embed_query(user_query)
        try:
            results = vectorstore._client.query.get(
                'Neurobot',
                ["text"]
            ).with_near_vector({
                "vector": query_embedding
            }).with_limit(no_of_docs).do()
        except weaviate.exceptions.UnexpectedStatusCodeException as e:
            print(f"Error fetching similar documents: {e}")
            return []
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            return []
        documents = results["data"]["Get"]['Neurobot']
        return [doc["text"] for doc in documents]
        # return []



        # result = vectorstore.similarity_search(query=user_query, k=no_of_docs)
        # text_result = ""
        # for r in result:
        #     text_result += '\n' + r.page_content
        # return text_result

    def vectorstore(embeddings):
        # WeaviateDB.create_schema()
        return LangchainWeaviate(
            client=WeaviateDB.client,
            index_name='NeuroBotVec',
            text_key="text",
            embedding=embeddings,
        )
    
    def create_schema():
        WeaviateDB.client.schema.delete_all()
        WeaviateDB.client.schema.get()
        schema = {
            "classes": [
                {
                    "class": os.getenv('WEAVIATE_INDEX_NAME'),
                    "description": f"Documents for {os.getenv('WEAVIATE_INDEX_NAME')}",
                    "vectorizer": "text2vec-openai",
                    "moduleConfig": {"text2vec-openai": {"model": "ada", "type": "text"}},
                    "properties": [
                        {
                            "dataType": ["text"],
                            "description": "The content of the paragraph",
                            "moduleConfig": {
                                "text2vec-openai": {
                                    "skip": False,
                                    "vectorizePropertyName": False,
                                }
                            },
                            "name": "content",
                        },
                    ],
                },
            ]
        }
        WeaviateDB.client.schema.create(schema)
