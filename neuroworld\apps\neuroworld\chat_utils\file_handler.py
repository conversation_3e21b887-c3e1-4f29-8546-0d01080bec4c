from http.client import HTTPException
import io
import os
import fitz
from docx import Document
from PyPDF2 import PdfReader

class FileHandler:
    def __init__(self):
        self.text = ""
  
    @staticmethod
    def get_text(file_path=None, file_name=None, file=None):
        fh = FileHandler()
        if file:  # If it's an UploadFile object
            fh.__get_text_from_file(file)
        else:  # If it's a file path
            fh.__get_text_from_path(file_path, file_name)
        return fh.text

    def __get_text_from_path(self, file_path, original_filename):
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
            
        # Use the original filename for extension detection
        extension = original_filename.split('.')[-1].lower()
        
        if extension == 'doc':
            # TODO: Add Support for doc file
            pass
        elif extension == 'docx':
            with open(file_path, "rb") as file:
                contents = file.read()
            file_like_object = io.BytesIO(contents)
            doc = Document(file_like_object)
            self.text += "\n".join([para.text for para in doc.paragraphs])
        elif extension == 'pdf':
            reader = PdfReader(file_path)
            for page in reader.pages:
                self.text += page.extract_text()
        else:
            raise ValueError(f'Invalid file type: {extension}')

    def __get_text_from_file(self, file):
        file_extension = file.filename.split('.')[-1].lower()
        contents = file.file.read() 
        
        if file_extension == 'pdf':
            pdf_document = fitz.open(stream=contents, filetype=file_extension)
            for page_num in range(len(pdf_document)):
                page = pdf_document.load_page(page_num)
                self.text += page.get_text()
        elif file_extension == 'docx':
            file_like_object = io.BytesIO(contents)
            doc = Document(file_like_object)
            self.text += "\n".join([para.text for para in doc.paragraphs])
        else:
            raise ValueError(f'Invalid file type: {file_extension}')