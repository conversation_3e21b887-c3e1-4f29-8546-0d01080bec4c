import json
import os
from django.core.management.base import BaseCommand
from django.db.utils import IntegrityError
from apps.neuroworld.models import PlanFeature, SubscriptionPlan, PlanTier, BillingPeriod
from django.conf import settings
import re

PLAN_FEATURES = {
    "FREE": [
        {"description": "Track 5 habits, build streaks, score NEURO Points.", "icon": "habits"},
        {"description": "Ask Myla about brain health and habit-building.", "icon": "myla_chat"},
        {"description": "Start your NEURO City with 5 plots.", "icon": "neurocity"},
        {"description": "Play 12 cognitive assessment and improvement games.", "icon": "neuro_games"}
    ],
    "HABITS": [
        {"description": "Track infinite habits, build streaks, score NEURO Points.", "icon": "habits"},
        {"description": "Ask Myla about brain health and habit-building.", "icon": "myla_chat"},
        {"description": "Grow your NEURO City with infinite plots.", "icon": "neurocity"},
        {"description": "Play all cognitive assessment and improvement games.", "icon": "neuro_games"}
    ],
    "SUMMIT": [
        {"description": "Track infinite habits, build streaks, score NEURO Points.", "icon": "habits"},
        {"description": "In-depth answers and personalised lifestyle plans from Myla.", "icon": "myla_chat"},
        {"description": "Grow your NEURO City with infinite plots.", "icon": "neurocity"},
        {"description": "Community spaces and hundreds of resources in the Summit.", "icon": "community"},
        {"description": "Join livestreams and access an archive of past events.", "icon": "livestreams"},
        {"description": "Play all cognitive assessment and improvement games.", "icon": "neuro_games"}
    ],
    "CLINICAL": [
        {"description": "A fully-integrated lifestyle and healthcare solution in your pocket.", "icon": "clinical_solution"}
    ]
}

class Command(BaseCommand):
    help = "Seed SubscriptionPlan records from a JSON fixture"

    def handle(self, *args, **kwargs):
        file_path = os.path.join(os.path.dirname(__file__), "../../fixtures/subscriptions_data.json")

        try:
            with open(file_path, "r", encoding="utf-8") as file:
                plans_data = json.load(file)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error reading JSON file: {str(e)}"))
            return

        for plan_data in plans_data:
            tier_str = plan_data.get("tier")
            name = plan_data.get("name")
            price = plan_data.get("price")
            billing_period_str = plan_data.get("billing_period")
            product_id = plan_data.get("product_id")
            duration_days = plan_data.get("duration_days")

            # Replace {{ENV}} in product_id if it exists
            if isinstance(product_id, str):
                product_id = re.sub(r"{{ENV}}", settings.ENVIRONMENT_NAME.lower(), product_id)

            try:
                tier = getattr(PlanTier, tier_str.upper())
            except AttributeError:
                self.stdout.write(self.style.ERROR(f"Invalid tier '{tier_str}' for plan '{name}'"))
                continue

            # billing_period can be None for free tier
            billing_period = None
            if billing_period_str:
                try:
                    billing_period = getattr(BillingPeriod, billing_period_str.upper())
                except AttributeError:
                    self.stdout.write(self.style.ERROR(f"Invalid billing_period '{billing_period_str}' for plan '{name}'"))
                    continue

            try:
                plan_obj, created = SubscriptionPlan.objects.get_or_create(
                    tier=tier,
                    billing_period=billing_period,
                    defaults={
                        "name": name,
                        "price": price,
                        "product_id": product_id,
                        "duration_days": duration_days
                    }
                )
                if not created:
                    plan_obj.name = name
                    plan_obj.price = price
                    plan_obj.product_id = product_id
                    plan_obj.duration_days = duration_days
                    plan_obj.save()

            except IntegrityError as e:
                self.stdout.write(self.style.ERROR(f"Integrity error for plan '{name}': {str(e)}"))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error creating/updating plan '{name}': {str(e)}"))

        for tier_str, features in PLAN_FEATURES.items():
            # Clear existing features for this tier
            PlanFeature.objects.filter(tier=tier_str.upper()).delete()

            for feature in features:
                PlanFeature.objects.create(
                    tier=tier_str.upper(),
                    description=feature["description"],
                    icon=feature.get("icon", None)
                )

        self.stdout.write(self.style.SUCCESS("Seeded SubscriptionPlan records successfully."))
        self.stdout.write(self.style.SUCCESS("Seeded PlanFeature records successfully."))
