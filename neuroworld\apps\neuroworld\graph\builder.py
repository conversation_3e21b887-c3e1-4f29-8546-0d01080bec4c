from ..graph import nodes
from langgraph.graph import StateGraph, START, END
from .state import State
from ..graph.conditional_edges import (
  select_category_node_workflow_selector,
)

workflow = StateGraph(State)
workflow.add_node("select_category_node", nodes.select_category_node)
workflow.add_node("goals_options_node", nodes.goals_options_node)
workflow.add_node("character_switch_node", nodes.character_switch_node)
workflow.add_node("days_selection_node", nodes.days_selection_node)
workflow.add_node("confirmation_question_node", nodes.confirmation_question_node)

workflow.add_conditional_edges(START, select_category_node_workflow_selector)

graph_builder = workflow.compile()
